﻿using AutoMapper;
using SmartpaMobiles.Business.Request;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.ComponentModel.DataAnnotations;
using SmartpaMobiles.Business.Request;
using System.Linq;

namespace SmartpaMobiles.Business
{
    public class ClientsBusiness : IClientsBusiness
    {
        private SmartpaMobiles.Data.Models.SmartpaContext dbContext;
        //private Microsoft.Extensions.Hosting.IHostEnvironment hostEnvironment;
        //private IConfiguration configuration;
        private IMapper mapper;
        private ILogger logger;

        public ClientsBusiness(SmartpaMobiles.Data.Models.SmartpaContext dbContext, IMapper mapper, ILogger<ClientsBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<PagedData<List<SmartpaMobiles.Data.DTOs.ClientView>>> GetClients(ReadPagedDataParameters parameters)
        {
            try
            {
                //Validation
                int resultsCount;
                parameters.Filter = (parameters.Filter ?? "").Trim();
                if (parameters.PageIndex <= 0)
                {
                    parameters.PageIndex = 1;
                }
                if (parameters.PageSize <= 0)
                {
                    parameters.PageSize = 10;
                }

                //Query
                IQueryable<SmartpaMobiles.Data.Models.Client> query = this.dbContext.Clients.AsQueryable();
                query = query.Where(c => c.OfficerId == parameters.OfficerId);
                if (parameters.Filter != "")
                {
                    query = query.Where(c => c.FirstLastName.Contains(parameters.Filter) || c.Mobile1.Contains(parameters.Filter));
                }

                //Διαβάζει το σύνολο των records.
                resultsCount = query.Count();

                //Διαβάζει τα δεδομένα.
                List<SmartpaMobiles.Data.Models.Client> clients = await query.AsNoTrackingWithIdentityResolution().Skip((parameters.PageIndex - 1) * parameters.PageSize).Take(parameters.PageSize).ToListAsync();
                List<SmartpaMobiles.Data.DTOs.ClientView> clientsDTOs = mapper.Map<List<SmartpaMobiles.Data.Models.Client>, List<SmartpaMobiles.Data.DTOs.ClientView>>(clients);

                //Response
                PagedData<List<SmartpaMobiles.Data.DTOs.ClientView>> pagedData = new PagedData<List<Data.DTOs.ClientView>>();
                pagedData.Data = clientsDTOs;
                pagedData.DataTotalCount = resultsCount;
                return pagedData;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "Parameters=" + parameters.ToString() });
                throw;
            }
        }

        public async Task<List<SmartpaMobiles.Data.DTOs.ClientLI>> GetClientsLI(int officeId)
        {
            try
            {
                //Validation

                //Query
                IQueryable<SmartpaMobiles.Data.Models.Client> query = this.dbContext.Clients.AsNoTrackingWithIdentityResolution().AsQueryable();
                query = query.Where(x => x.OfficerId == officeId).Take(5000).OrderBy(x => x.FirstLastName).ThenBy(x => x.MiddleName);  //TODO: να φύγει το Take(100) αλλά κολλάει με πολλά contacts.

                //Διαβάζει τα δεδομένα.
                List<SmartpaMobiles.Data.Models.Client> clients = await query.ToListAsync();
                List<SmartpaMobiles.Data.DTOs.ClientLI> contactLIs = mapper.Map<List<SmartpaMobiles.Data.Models.Client>, List<SmartpaMobiles.Data.DTOs.ClientLI>>(clients);

                return contactLIs;
            }
            catch (Exception ex)
            {
                logger.Log(Microsoft.Extensions.Logging.LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }


        public async Task<Data.Models.Client?> GetClient(int clientId)
        {
            try
            {
                //Query
                IQueryable<Client> query = dbContext.Clients.AsNoTrackingWithIdentityResolution().Include(x => x.Participations).ThenInclude(x => x.Appointment)/*.Include(x => x.Participations)*/.AsQueryable();
                query = query.Where(c => c.ClientId == clientId);

                //Καθαρίζει επιπλέον δεδομένα που δεν χρειάζονται.
                Client? client = await query.FirstOrDefaultAsync<Client>();
                if (client != null)
                {
                    foreach (Participation p in client.Participations)
                    {
                        if (p.Appointment != null)
                        {
                            p.Appointment!.Participations = new List<Participation>();
                        }
                    }
                }

                return client;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "ClientId=" + clientId.ToString() });
                throw;
            }
        }

        public async Task<int> CreateOrUpdateClient(Client client)
        {
            try
            {
                //Validation
                if (client == null)
                {
                    throw new Exception(Resources.GlobalResources.InvalidDataMessage);
                }

                //Query
                this.dbContext.Attach(client);
                await this.dbContext.SaveChangesAsync();

                //Return the ClientId
                return client.ClientId;
            }
            catch (ApplicationException ex)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { client.ClientId });
                throw;
            }
        }

        public async Task DeleteClient(int clientId)
        {
            Client? contact = await this.dbContext.Clients.Where(x => x.ClientId == clientId).FirstAsync();
            if (contact != null)
            {
                contact.ObjectState = ObjectState.Deleted;
                this.dbContext.Attach(contact);
                this.dbContext.SaveChanges();
            }
        }
    }
}
