﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls;
using SmartpaMobiles.UI.Pages;

namespace SmartpaMobiles.UI
{
    public partial class AppShell : Shell
    {
        [Inject]
        ILogger<LoginPage> logger { get; set; }

        public string AppVersion { get; private set; }

        public AppShell()
        {
            InitializeComponent();

            // Get the version from your app
            AppVersion = $"Version {AppInfo.Current.VersionString}";
            
            // Set the binding context to this instance
            BindingContext = this;

            Routing.RegisterRoute(nameof(ClientPage), typeof(ClientPage));

            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                Console.WriteLine($"Unhandled exception: {e.ExceptionObject}");
                logger.LogError((Exception)e.ExceptionObject, ((Exception)e.ExceptionObject).Message);
            };
            TaskScheduler.UnobservedTaskException += (sender, e) =>
            {
                Console.WriteLine($"Unobserved task exception: {e.Exception}");
                e.SetObserved();
                logger.LogError(e.Exception, (e.Exception).Message);
            };
        }

        private async void OnSignOutClicked(object sender, EventArgs e)
        {
            //bool confirmSignOut = await DisplayAlert("Sign Out", "Are you sure you want to sign out?", "Yes", "No");
            //if (confirmSignOut)
            {
                // Add your sign-out logic here, such as clearing user data or tokens
                //Application.Current.cl.Clear(); // Example: clear app-wide properties
                SecureStorage.Default.Remove("Token"); // Example: remove secure tokens

                // Redirect to the Login Page
                await Current.GoToAsync("//LoginPage");

                // Close the app (optional)
                //CloseApplication();
            }
        }

        private void CloseApplication()
        {
            // Close the application - works only on platforms supporting it
            Application.Current.Quit();
        }
    }
}
