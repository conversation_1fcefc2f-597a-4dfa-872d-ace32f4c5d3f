﻿using SmartpaMobiles.Business.Request;
using SmartpaMobiles.Business.Response;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Shared;

namespace SmartpaMobiles.Business
{
    public interface IUsersBusiness
    {
        Task<bool> CheckUsernamePasswordExists(string username, string password);
        Task<User?> GetUser(string username, string password);
        Task<User?> GetUser(int userId);
        
    }
}