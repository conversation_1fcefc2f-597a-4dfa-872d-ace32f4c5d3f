<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="SmartpaMobiles.UI.Pages.AppointmentPage"
             xmlns:project="clr-namespace:SmartpaMobiles.UI"
             xmlns:picker="clr-namespace:Syncfusion.Maui.Picker;assembly=Syncfusion.Maui.Picker"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             xmlns:converters="clr-namespace:SmartpaMobiles.UI.Utilities"
             xmlns:editors="clr-namespace:Syncfusion.Maui.Inputs;assembly=Syncfusion.Maui.Inputs"
             Title="{x:Static project:Pages.Resources.AppointmentPageResources.Title}">

    <ContentPage.Resources>
        <ResourceDictionary>
            <toolkit:InvertedBoolConverter x:Key="InvertedBoolConverter" />
            <converters:BooleanToStrikethroughConverter x:Key="booleanToStrikethroughConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>

    <ContentPage.ToolbarItems>
        <ToolbarItem Text="{x:Static project:Resources.GlobalResources.Save}" Order="Primary" IconImageSource="check.png" Clicked="saveToolbarItem_Clicked">
        </ToolbarItem>
        <ToolbarItem Text="{x:Static project:Resources.GlobalResources.Delete}" Order="Secondary" IconImageSource="delete.png" Clicked="deleteToolbarItem_Clicked">
        </ToolbarItem>
    </ContentPage.ToolbarItems>

    <VerticalStackLayout>
        <Label Text="{x:Static project:Pages.Resources.AppointmentPageResources.Subject}" StyleClass="LeftTopMarginLabel"></Label>
        <editors:SfComboBox x:Name="subjectCmbBox" IsEditable="true" BackgroundColor="Transparent" NoResultsFoundText="{x:Static project:Resources.GlobalResources.NoResultsFound}" TextMemberPath="ServiceName" DisplayMemberPath ="ServiceName" SelectedValuePath="AppointmentServiceId" SelectionChanged="subjectCmbBox_SelectionChanged" />


        <Label x:Name="clientLabel" Text="{x:Static project:Pages.Resources.AppointmentPageResources.Client}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <editors:SfComboBox x:Name="clientCmbBox" IsEditable="true" IsFilteringEnabled="true" TextSearchMode="Contains" BackgroundColor="Transparent" HighlightedTextColor="Transparent" SelectionTextHighlightColor="Gainsboro" NoResultsFoundText="{x:Static project:Resources.GlobalResources.NoResultsFound}" TextMemberPath="FirstLastName" DisplayMemberPath ="FirstLastName" SelectedValuePath="ClientId" />
        <Button MaximumHeightRequest="40" Text="{x:Static project:Pages.Resources.AppointmentPageResources.NewClient}" HorizontalOptions="End" WidthRequest="100" HeightRequest="30" Clicked="NewClient_Clicked" Margin="0,0,10,0" />

        <Label Text="{x:Static project:Pages.Resources.AppointmentPageResources.Date}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <!--<DatePicker Date="{Binding StartDateOnly}" DateSelected="StartDatePicker_DateSelected" />-->
        <Entry x:Name="startDateEntry" IsReadOnly="true" Focused="startDateEntry_Focused">
            <Entry.GestureRecognizers>
                <TapGestureRecognizer Tapped="startDateEntry_Tapped"  />
            </Entry.GestureRecognizers>
        </Entry>
        <picker:SfDatePicker x:Name="startDateSfPicker" SelectedDate="{Binding StartDateOnly}" Format="dd_MM_yyyy" Mode="Dialog" SelectionChanged="startDateSfPicker_SelectionChanged"></picker:SfDatePicker>

        <Label x:Name="startTimeLabel" Text="{x:Static project:Pages.Resources.AppointmentPageResources.StartTime}" IsVisible="{Binding AllDay, Converter={StaticResource InvertedBoolConverter}}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <!--<TimePicker x:Name="startTimePicker" Time="{Binding StartTimeOnly}" Focused="startTimePicker_Focused" Format="HH:mm" IsVisible="{Binding AllDay, Converter={StaticResource InvertedBoolConverter}}" />-->
        <Entry x:Name="startTimeEntry" IsReadOnly="true" Focused="startTimeEntry_Focused" IsVisible="{Binding AllDay, Converter={StaticResource InvertedBoolConverter}}">  
           <Entry.GestureRecognizers>  
               <TapGestureRecognizer Tapped="startTimeEntry_Tapped"  />  
           </Entry.GestureRecognizers>  
        </Entry>
        <picker:SfTimePicker x:Name="startTimeSfPicker" SelectedTime="{Binding StartTimeOnly}" Format="HH_mm" Mode="Dialog" SelectionChanged="startTimeSfPicker_SelectionChanged2" IsVisible="{Binding AllDay, Converter={StaticResource InvertedBoolConverter}}" />

        <Label x:Name="endTimeLabel" Text="{x:Static project:Pages.Resources.AppointmentPageResources.EndTime}" IsVisible="{Binding AllDay, Converter={StaticResource InvertedBoolConverter}}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <!--<TimePicker x:Name="endTimePicker" Time="{Binding EndTimeOnly}"  Format="HH:mm" IsVisible="{Binding AllDay, Converter={StaticResource InvertedBoolConverter}}"/>-->
        <Entry x:Name="endTimeEntry" IsReadOnly="true" Focused="endTimeEntry_Focused" IsVisible="{Binding AllDay, Converter={StaticResource InvertedBoolConverter}}">
            <Entry.GestureRecognizers>
                <TapGestureRecognizer Tapped="endTimeEntry_Tapped"  />
            </Entry.GestureRecognizers>
        </Entry>
        <picker:SfTimePicker x:Name="endTimeSfPicker" SelectedTime="{Binding EndTimeOnly}" Format="HH_mm" Mode="Dialog" SelectionChanged="endTimeSfPicker_SelectionChanged" IsVisible="{Binding AllDay, Converter={StaticResource InvertedBoolConverter}}" />


        <Label x:Name="isAllDayLabel" Text="{x:Static project:Pages.Resources.AppointmentPageResources.AllDay}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <Switch x:Name="isAllDaySwitch" IsToggled="{Binding AllDay}" HorizontalOptions="Start" Toggled="isAllDaySwitch_Toggled"/>

        <Label x:Name="labelLabel" Text="{x:Static project:Pages.Resources.AppointmentPageResources.Label}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <Picker x:Name="labelPicker" Title="{x:Static project:Pages.Resources.AppointmentPageResources.Label}" ItemDisplayBinding="{Binding Title}"></Picker>

        <Label x:Name="blockOthersLabel" Text="{x:Static project:Pages.Resources.AppointmentPageResources.BlockOthers}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <Switch x:Name="blockOthersSwitch" IsToggled="{Binding Block}" HorizontalOptions="Start" />


        <Label Text="  " HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <Editor Text="{Binding Notes}" HeightRequest="100"  />
        <!--<Label Text="{x:Static project:Pages.Resources.ClientPageResources.Age}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <Label Text="{Binding Age}" />
        <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Amka}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <Entry Text="{Binding Amka}"></Entry>
        <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Mobile}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <Entry Text="{Binding Mobile1}"></Entry>
        <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Phone}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <Entry Text="{Binding Phone1}"></Entry>
        <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Fax}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
        <Entry Text="{Binding Fax}"></Entry>-->
    </VerticalStackLayout>
</ContentPage>