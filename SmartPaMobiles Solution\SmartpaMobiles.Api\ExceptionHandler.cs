﻿using Microsoft.Extensions.Hosting.Internal;

namespace SmartpaMobiles.Api
{
    public class ExceptionHandler
    {
        Microsoft.Extensions.Hosting.IHostEnvironment hostingEnvironment;
        string productName;

        public ExceptionHandler(Microsoft.Extensions.Hosting.IHostEnvironment hostingEnvironment, string productName) 
        {
            this.hostingEnvironment = hostingEnvironment;
            this.productName = productName;
        }


        public void LogException(Exception ex)
        {
            StreamWriter writer = null;
            try
            {
                if (ex.GetType() != typeof(System.Threading.ThreadAbortException))
                {
                    string exceptionsFilePath = this.hostingEnvironment.ContentRootPath + @"\" + productName + "Exceptions.txt";

                    if (System.IO.File.Exists(exceptionsFilePath) == false)  //Αν το αρχείο δεν υπάρχει
                    {
                        FileStream stream = System.IO.File.Create(exceptionsFilePath);  //τοτε το δημιουργεί
                        stream.Close();  //και το κλείνει
                    }
                    writer = System.IO.File.AppendText(exceptionsFilePath);
                    writer.WriteLine("Product: " + productName + "\r\nDATE: " + DateTime.Now.ToString() + "\r\nSTACK TRACE: " + ex.StackTrace + "\r\nERROR: " + ex.Message + "\r\n(METHOD: " + ex.TargetSite + ")" + "\r\n(Source: " + ex.Source + ")" + "\r\nErrorID: " + Guid.NewGuid().ToString().ToUpper() + "\r\n");
                    if (ex.InnerException != null)
                    {
                        writer.WriteLine("InnerException: \r\nSTACK TRACE: " + ex.InnerException.StackTrace + "\r\nERROR: " + ex.InnerException.Message + "\r\n(METHOD: " + ex.InnerException.TargetSite + ")" + "\r\n");
                    }
                }
            }
            catch
            {
            }
            finally
            {
                if (writer != null)
                {
                    writer.Close();
                }
            }
           
        }
    }
}
