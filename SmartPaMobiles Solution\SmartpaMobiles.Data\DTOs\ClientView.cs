﻿using SmartpaMobiles.Data.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.Data.DTOs
{
    public class ClientView
    {
        [Key]
        public int ClientId { get; set; }

        public int OfficerId { get; set; }

        public string FirstLastName { get; set; } = "";

        public string MiddleName { get; set; } = "";

        public string Email { get; set; } = null!;

        public string Phone1 { get; set; } = null!;

        public string Mobile1 { get; set; } = null!;

        public string Occupation { get; set; } = "";

        public string Ama { get; set; } = "";

        public string Amka { get; set; } = "";

        public string Afm { get; set; } = "";

        public string Initials
        {
            get
            {
                string fixedFirstLastName = FirstLastName.Replace("  ", " ").Trim();
                if (fixedFirstLastName.Length > 0)
                {
                    return fixedFirstLastName.Split(' ')[0].Substring(0, 1).ToUpper() + (FirstLastName.Split(' ').Length > 1 ? (FirstLastName.Split(' ')[1].Length > 0 ? FirstLastName.Split(' ')[1][0].ToString().ToUpper() : string.Empty) : "");
                }
                else
                {
                    return "";
                }
            }
        }
    }
}
