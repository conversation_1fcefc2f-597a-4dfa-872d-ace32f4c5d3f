﻿using CommunityToolkit.Maui.Core.Extensions;
using MvvmHelpers;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Shared;
using SmartpaMobiles.UI.Auth;
using SmartpaMobiles.UI.WebApiClient;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.UI.ViewModels
{
    internal class ClientsViewModel : BaseViewModel
    {
        protected ClientsWebApiClient clientsWebApiClient;
        protected List<Data.DTOs.ClientView> originalClients;
        //protected ObservableRangeCollection<Data.DTOs.ClientView> currentClients;
        public ObservableRangeCollection<Data.DTOs.ClientView> Clients
        {
            get; set;
            //get
            //{
            //    return currentClients;
            //}
            //set
            //{
            //    this.currentClients = value;
            //}
        }
        protected string filter = "";
        public string Filter
        {
            get
            {
                return filter;
            }
            set
            {
                this.filter = value;
                if (filter.Trim() != "")
                {
                    Clients.Clear();
                    var temp = this.originalClients.Where(x => x.FirstLastName.ToLower().Contains(Filter.ToLower()) || x.Mobile1.Contains(Filter.ToLower()) || x.Phone1.Contains(Filter.ToLower()) || x.Email.Contains(Filter.ToLower()) || x.Amka.Contains(Filter.ToLower())).ToList();
                    Clients.AddRange(temp);
                }
                else
                {
                    Clients.Clear();
                    Clients.AddRange(this.originalClients);
                }
            }
        }

        public ClientsViewModel(ClientsWebApiClient clientsWebApiClient)
        {
            originalClients = new List<Data.DTOs.ClientView>();
            Clients = new ObservableRangeCollection<Data.DTOs.ClientView>();
            this.clientsWebApiClient = clientsWebApiClient;
        }

        public async Task Refresh()
        {
            PagedData<List<Data.DTOs.ClientView>> result = await this.clientsWebApiClient.GetClients(AuthenticatedUserData.OfficerId, "", 0, 5000);
            this.originalClients.Clear();
            this.originalClients = result.Data!.OrderBy(x => x.FirstLastName).ToList()!;

            if (filter.Trim() != "")
            {
                Clients.Clear();
                Clients.AddRange(this.originalClients.Where(x => x.FirstLastName.ToLower().Contains(Filter.ToLower()) || x.Mobile1.Contains(Filter.ToLower()) || x.Phone1.Contains(Filter.ToLower()) || x.Email.Contains(Filter.ToLower()) || x.Amka.Contains(Filter.ToLower())));
            }
            else
            {
                Clients.Clear();
                Clients.AddRange(this.originalClients);
            }
        }


    }
}
