﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartpaMobiles.Data.Models;

public partial class User : IObjectState
{
    public User()
    {
        this.ObjectState = ObjectState.Unchanged;
    }
    public int UserId { get; set; }

    public int RoleId { get; set; }

    public string FirstLastName { get; set; } = null!;

    public string MiddleName { get; set; } = null!;

    public string Username { get; set; } = null!;

    public string Password { get; set; } = null!;

    public string Email { get; set; } = null!;

    public byte[] RowVersion { get; set; } = null!;

    public string Phone1 { get; set; } = null!;

    public string Mobile1 { get; set; } = null!;

    public string Fax { get; set; } = null!;

    public string InternalNotes { get; set; } = null!;

    public bool Suspended { get; set; }

    public bool CanEditAppointment { get; set; }

    public bool CanDeleteAppointment { get; set; }

    public bool CanEditAppointmentSmsSending { get; set; }

    public virtual ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();

    public virtual Officer? Officer { get; set; }

    public virtual ICollection<RestrictedOfficer> RestrictedOfficers { get; set; } = new List<RestrictedOfficer>();

    public virtual Role Role { get; set; } = null!;

    [NotMapped]
    public ObjectState ObjectState { get; set; }
}