{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "jwt": {"key": "aasdfkhascahfcasdklfhacnlsdkjfhalkjhaslfjhasdkfahsldfkjhasdf"}, "Serilog": {"Using": ["Serilog.Sinks.File", "Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "./logs/log-.txt", "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.JsonFormatter", "rollingInterval": "Day", "retainedFileCountLimit": "30", "fileSizeLimitBytes": "1000000"}}], "Enrich": ["WithExceptionDetails"]}}