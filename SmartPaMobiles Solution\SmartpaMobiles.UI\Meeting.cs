﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.UI
{
    class Meeting
    {

        public DateTime From { get; set; }
        public DateTime To { get; set; }
        public bool IsAllDay { get; set; }
        public string EventName { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public TimeZoneInfo? StartTimeZone { get; set; } = null;
        public TimeZoneInfo? EndTimeZone { get; set; } = null;
        public Brush? Background { get; set; } = null;
        public Color? TextColor { get; set; } = null;
        public object? RecurrenceId { get; set; } = null;
        public object? Id { get; set; } = null;
        public string RecurrenceRule { get; set; } = string.Empty;
        public ObservableCollection<DateTime> RecurrenceExceptions { get; set; }
    }
}
