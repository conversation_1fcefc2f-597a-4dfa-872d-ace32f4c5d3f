using Microsoft.Extensions.Logging;
using SmartpaMobiles.ApiShared.Response;
using SmartpaMobiles.Shared.Cryptography.AES;
using SmartpaMobiles.UI.Auth;
using SmartpaMobiles.UI.Pages.Resources;
using SmartpaMobiles.UI.Resources;
using SmartpaMobiles.UI.WebApiClient;
using System.Text;

namespace SmartpaMobiles.UI.Pages;

public partial class LoginPage : ContentPage
{
    private readonly ILogger<LoginPage> logger;
    private AuthenticationWebApiClient authenticationWebApiClient;
    private ILoginService loginService;
    private string username;
    private string password;

    public LoginPage(ILogger<LoginPage> logger, AuthenticationWebApiClient authenticationWebApiClient, ILoginService loginService)
    {
        this.logger = logger;
        this.authenticationWebApiClient = authenticationWebApiClient;
        this.loginService = loginService;

        InitializeComponent();

#if DEBUG
        usernameEntry.Text = "33";
        passwordEntry.Text = "33";
#endif
    }

    private async void loginBtnClicked(object sender, EventArgs e)
    {
        try
        {
            this.activityIndicator.IsRunning = true;

            //string encryptedUsername = Convert.ToBase64String(Encoding.UTF8.GetBytes(AesCryptography.Encrypt(usernameEntry.Text)));
            //string encryptedPassword = Convert.ToBase64String(Encoding.UTF8.GetBytes(AesCryptography.Encrypt(passwordEntry.Text)));
            string encryptedUsername = AesCryptography.Encrypt(usernameEntry.Text);
            string encryptedPassword = AesCryptography.Encrypt(passwordEntry.Text);

            LoginResponse loginResponse = await authenticationWebApiClient.Login(encryptedUsername, encryptedPassword);
            if (loginResponse.LoginResult == SmartpaMobiles.ApiShared.Response.LoginResult.Ok)
            {
                await loginService.Login(loginResponse.Token!);
                //navigationManager.NavigateTo("");
                await Shell.Current.GoToAsync("//" + nameof(CalendarPage));
            }
            else if (loginResponse.LoginResult == LoginResult.InvalidUsernamePassword)
            {
                await DisplayAlert("", LoginPageResources.InvalidUsernamePassword, GlobalResources.Close);
                //this.loginResultMessage = Resources.LoginResource.InvalidUsernamePassword;
            }
            else if (loginResponse.LoginResult == LoginResult.AccountExpired)
            {
                //this.loginResultMessage = Resources.LoginResource.AccountExpired;
            }
        }
        catch (Exception ex)
        {
            this.activityIndicator.IsRunning = false;
            logger.LogError(ex, ex.Message);
            //new ErrorNotifier(dialogService).ShowError(GlobalResource.ErrorOccured, "");
            await DisplayAlert("", GlobalResources.UnexpectedErrorHappened, GlobalResources.Close);
        }
        finally
        {
            this.activityIndicator.IsRunning = false;
        }
    }
}