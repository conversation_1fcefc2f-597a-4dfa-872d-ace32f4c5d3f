﻿
using SmartpaMobiles.Business;
using SmartpaMobiles.Shared.Authorization;
using SmartpaMobiles.ApiShared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using System.Text;
using Asp.Versioning;
using AutoMapper;
using SmartpaMobiles.Shared;
using SmartpaMobiles.Business.Request;
using SmartpaMobiles.ApiShared.Request;
using SmartpaMobiles.Data.Models;


namespace SmartpaMobiles.Api.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class ClientsController : ControllerBase
    {
        private IClientsBusiness clientsBusiness;
        private ILogger<ClientsController> logger;
        private IMapper mapper;
        private IWebApiCallerInfo webApiCallerInfo;

        public ClientsController(IClientsBusiness clientBusiness, ILogger<ClientsController> logger, IMapper mapper, IWebApiCallerInfo webApiCallerInfo)
        {
            this.clientsBusiness = clientBusiness;
            this.logger = logger;
            this.mapper = mapper;
            this.webApiCallerInfo = webApiCallerInfo;
        }


        /// <summary>
        /// Retrieves transforms in paged mode.
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ApiResponse<PagedData<List<SmartpaMobiles.Data.DTOs.ClientView>>>> GetClients([FromBody] RequestDataParameters parameters)
        {
            try
            {
                //Map data
                ReadPagedDataParameters readPagedDataParameters = this.mapper.Map<ReadPagedDataParameters>(parameters);

                ////Validation
                //if (readPagedDataParameters.OfficerId != this.webApiCallerInfo.OfficerId)
                //{
                //    return new ApiResponse<PagedData<List<SmartpaMobiles.Data.DTOs.ClientView>>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = "OfficerId missmacth." };
                //}

                //Query
                PagedData<List<SmartpaMobiles.Data.DTOs.ClientView>> data = await this.clientsBusiness.GetClients(readPagedDataParameters);

                //Response
                return new ApiResponse<PagedData<List<SmartpaMobiles.Data.DTOs.ClientView>>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, parameters);
                return new ApiResponse<PagedData<List<SmartpaMobiles.Data.DTOs.ClientView>>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        /// <summary>
        /// Returns all the Clients of Tenant. This list should be used for displaying in lists, dropdowns.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("li")]
        public async Task<ApiResponse<List<SmartpaMobiles.Data.DTOs.ClientLI>>> GetClientsLI([FromQuery] int officerId)
        {
            try
            {
                //Query
                List<SmartpaMobiles.Data.DTOs.ClientLI> data = await this.clientsBusiness.GetClientsLI(officerId);

                //Response
                return new ApiResponse<List<SmartpaMobiles.Data.DTOs.ClientLI>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, officerId);
                return new ApiResponse<List<SmartpaMobiles.Data.DTOs.ClientLI>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }


        [HttpGet]
        [Route("{clientId}")]
        public async Task<ApiResponse<Client?>> GetClient(string clientId)
        {
            try
            {
                //Query
                Client? client = await this.clientsBusiness.GetClient(Convert.ToInt32(clientId));

                //Response
                return new ApiResponse<Client?>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = client };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "ClientId=" + clientId);
                return new ApiResponse<Client?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }



        //[HttpPost]
        //[Route("create")]
        //public async Task<ApiResponse<Transform>> CreateTransform([FromBody] Transform transform)
        //{
        //    try
        //    {
        //        //Validation
        //        if (transform == null)
        //        {
        //            throw new Exception(GlobalResources.InvalidDataMessage);
        //        }

        //        if (ModelState.IsValid == false)
        //        {
        //            throw new Exception(ModelState.ValidationState.ToString());
        //        }

        //        //Query
        //        this.dbContext.Add(transform);
        //        await this.dbContext.SaveChangesAsync();

        //        ////Αν δημιουργούμε νέο transform
        //        //if (transform.ObjectState == ObjectState.Added)
        //        //{
        //        //    //Ξεκινάει το Python
        //        //    ScriptRuntimeSetup setup = Python.CreateRuntimeSetup(null);
        //        //    ScriptRuntime runtime = new ScriptRuntime(setup);
        //        //    ScriptEngine engine = Python.GetEngine(runtime);
        //        //    ScriptSource source = engine.CreateScriptSourceFromFile("Python1.py");
        //        //    ScriptScope scope = engine.CreateScope();
        //        //    List<String> argv = new List<String>();
        //        //    //Do some stuff and fill argv
        //        //    argv.Add("foo");
        //        //    argv.Add("bar");
        //        //    engine.GetSysModule().SetVariable("argv", argv);
        //        //    source.Execute(scope);
        //        //}

        //        //Αν δημιουργούμε νέο transform
        //        if (transform.ObjectState == ObjectState.Added)
        //        {
        //            Task.Run(() => this.TransformFiles(transform, configuration.GetValue<string>("FileTransformerConnectionString")));
        //        }

        //        //Response
        //        return new ApiResponse<Transform>() { ResultCode = ApiResponseResultCode.Ok, Data = transform };
        //    }
        //    catch (Exception ex)
        //    {
        //        new ExceptionHandler(hostEnvironment, "SmartpaMobiles.Api").LogException(ex);
        //        return new ApiResponse<Transform>() { ResultCode = ApiResponseResultCode.Exception, Exception = ex };
        //    }
        //}

        [HttpPost]
        [Route("createorupdate")]
        public async Task<ApiResponse<Client>> CreateOrUpdateClient([FromBody] Client client)
        {
            try
            {
                //Validation
                if (client == null)
                {
                    throw new Exception(SmartpaMobiles.Api.Resources.GlobalResources.InvalidDataMessage);
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.Values.ToString());
                }

                //Query
                int clientId = await this.clientsBusiness.CreateOrUpdateClient(client);

                //Response
                Client? conctact = await this.clientsBusiness.GetClient(clientId);
                return new ApiResponse<Client>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = client };
            }
            catch (ApplicationException ex)
            {
                this.logger.LogError(ex, ex.Message, client);
                return new ApiResponse<Client>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, client);
                return new ApiResponse<Client>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpDelete]
        //[Route("{clientId}")]
        public ApiResponse DeleteClient([FromQuery] int clientId)
        {
            try
            {
                //TODO: να μπει έλεγχος ότι η διαγραφή γίνεται από User με το ίδιο TenantId.

                this.clientsBusiness.DeleteClient(clientId);

                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "ClientId=" + clientId);
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }
    }
}
