﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SmartpaMobiles.UI.Pages.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ClientPageResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ClientPageResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("SmartpaMobiles.UI.Pages.Resources.ClientPageResources", typeof(ClientPageResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Διεύθυνση.
        /// </summary>
        internal static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Α.Φ.Μ..
        /// </summary>
        internal static string Afm {
            get {
                return ResourceManager.GetString("Afm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ηλικία.
        /// </summary>
        internal static string Age {
            get {
                return ResourceManager.GetString("Age", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ολοήμερο.
        /// </summary>
        internal static string AllDay {
            get {
                return ResourceManager.GetString("AllDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ολοήμερο.
        /// </summary>
        internal static string AllDayLowerWithSpace {
            get {
                return ResourceManager.GetString("AllDayLowerWithSpace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Α.Μ.Α..
        /// </summary>
        internal static string Ama {
            get {
                return ResourceManager.GetString("Ama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Α.Μ.Κ.Α..
        /// </summary>
        internal static string Amka {
            get {
                return ResourceManager.GetString("Amka", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τα πεδία AMKA και Κινητό είναι υποχρεωτικά..
        /// </summary>
        internal static string AmkaOrMobileRequired {
            get {
                return ResourceManager.GetString("AmkaOrMobileRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ραντεβού.
        /// </summary>
        internal static string AppointmentsTabTitle {
            get {
                return ResourceManager.GetString("AppointmentsTabTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ημ/νία Γέννησης.
        /// </summary>
        internal static string BirthDate {
            get {
                return ResourceManager.GetString("BirthDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πόλη.
        /// </summary>
        internal static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κωδικός.
        /// </summary>
        internal static string ClientId {
            get {
                return ResourceManager.GetString("ClientId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Δ.Ο.Υ..
        /// </summary>
        internal static string Doy {
            get {
                return ResourceManager.GetString("Doy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fax.
        /// </summary>
        internal static string Fax {
            get {
                return ResourceManager.GetString("Fax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ονοματεπώνυμο.
        /// </summary>
        internal static string FirstLastName {
            get {
                return ResourceManager.GetString("FirstLastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Στοιχεία.
        /// </summary>
        internal static string GeneralTabTitle {
            get {
                return ResourceManager.GetString("GeneralTabTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ασφαλιστικός Φορέας.
        /// </summary>
        internal static string InsuranceAgency {
            get {
                return ResourceManager.GetString("InsuranceAgency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Πατρώνυμο.
        /// </summary>
        internal static string MiddleName {
            get {
                return ResourceManager.GetString("MiddleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Κινητό.
        /// </summary>
        internal static string Mobile {
            get {
                return ResourceManager.GetString("Mobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επάγγελμα.
        /// </summary>
        internal static string Occupation {
            get {
                return ResourceManager.GetString("Occupation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Τηλέφωνο.
        /// </summary>
        internal static string Phone {
            get {
                return ResourceManager.GetString("Phone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ταχ. Κώδικας.
        /// </summary>
        internal static string PostalCode {
            get {
                return ResourceManager.GetString("PostalCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ασθενής.
        /// </summary>
        internal static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
    }
}
