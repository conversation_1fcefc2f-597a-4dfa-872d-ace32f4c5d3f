﻿using AutoMapper;
using SmartpaMobiles.Business.Request;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using System.ComponentModel.DataAnnotations;
using SmartpaMobiles.Business.Request;

namespace SmartpaMobiles.Business
{
    public class InsuranceAgenciesBusiness : IInsuranceAgenciesBusiness
    {
        private SmartpaMobiles.Data.Models.SmartpaContext dbContext;
        //private Microsoft.Extensions.Hosting.IHostEnvironment hostEnvironment;
        //private IConfiguration configuration;
        private IMapper mapper;
        private ILogger logger;

        public InsuranceAgenciesBusiness(SmartpaMobiles.Data.Models.SmartpaContext dbContext, IMapper mapper, ILogger<InsuranceAgenciesBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<List<SmartpaMobiles.Data.Models.InsuranceAgency>> GetAll()
        {
            try
            {
                //Validation
               
                //Query
                IQueryable<SmartpaMobiles.Data.Models.InsuranceAgency> query = this.dbContext.InsuranceAgencies.AsQueryable();

                //Διαβάζει τα δεδομένα.
                List<SmartpaMobiles.Data.Models.InsuranceAgency> agencies = await query.AsNoTrackingWithIdentityResolution().ToListAsync();

                //Response
                return agencies;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                throw;
            }
        }

      
    }
}
