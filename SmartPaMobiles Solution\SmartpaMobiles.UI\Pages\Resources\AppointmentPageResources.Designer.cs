﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SmartpaMobiles.UI.Pages.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class AppointmentPageResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal AppointmentPageResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("SmartpaMobiles.UI.Pages.Resources.AppointmentPageResources", typeof(AppointmentPageResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ολοήμερο.
        /// </summary>
        public static string AllDay {
            get {
                return ResourceManager.GetString("AllDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Το ραντεβού συμπίπτει με άλλο ραντεβού. Είστε σίγουρος ότι θέλετε να γίνει η αποθήκευση;.
        /// </summary>
        public static string AppointmentConflictOnSaveMessage {
            get {
                return ResourceManager.GetString("AppointmentConflictOnSaveMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Επιλέξτε έναν ασθενή..
        /// </summary>
        public static string AppointmentInvalidSelectClientMessage {
            get {
                return ResourceManager.GetString("AppointmentInvalidSelectClientMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Η ώρα έναρξης δεν μπορεί να είναι μεταγενέστερη της ώρας λήξης..
        /// </summary>
        public static string AppointmentInvalidStartEndTimeMessage {
            get {
                return ResourceManager.GetString("AppointmentInvalidStartEndTimeMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Υπάρχει πρόβλημα με τις ώρες έναρξης και λήξης. Επιλέξτε ξανά την ημερομηνία καθώς τις ώρες έναρξης και λήξης..
        /// </summary>
        public static string AppointmentStartEndTimeOnDifferentDayMessage {
            get {
                return ResourceManager.GetString("AppointmentStartEndTimeOnDifferentDayMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Εμποδίζει άλλα ραντεβού.
        /// </summary>
        public static string BlockOthers {
            get {
                return ResourceManager.GetString("BlockOthers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ασθενής.
        /// </summary>
        public static string Client {
            get {
                return ResourceManager.GetString("Client", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ημερομηνία.
        /// </summary>
        public static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Λήξη.
        /// </summary>
        public static string EndTime {
            get {
                return ResourceManager.GetString("EndTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ετικέτα.
        /// </summary>
        public static string Label {
            get {
                return ResourceManager.GetString("Label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Νέος.
        /// </summary>
        public static string NewClient {
            get {
                return ResourceManager.GetString("NewClient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Έναρξη.
        /// </summary>
        public static string StartTime {
            get {
                return ResourceManager.GetString("StartTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Θέμα.
        /// </summary>
        public static string Subject {
            get {
                return ResourceManager.GetString("Subject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ραντεβού.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
    }
}
