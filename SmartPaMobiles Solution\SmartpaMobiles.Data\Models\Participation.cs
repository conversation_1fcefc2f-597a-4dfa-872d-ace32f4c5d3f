﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartpaMobiles.Data.Models;

public partial class Participation : IObjectState
{

    public Participation()
    {
        this.ObjectState = ObjectState.Unchanged;
    }

    public int ParticipationId { get; set; }

    public int AppointmentId { get; set; }

    public int ClientId { get; set; }

    [ForeignKey("AppointmentId")]
    public Appointment? Appointment { get; set; } = null;

    [ForeignKey("ClientId")]
    public Client? Client { get; set; } = null;

    [NotMapped]
    public ObjectState ObjectState { get; set; }
}