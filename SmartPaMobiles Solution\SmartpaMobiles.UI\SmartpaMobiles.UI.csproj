﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net8.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
		The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
		When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
		The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
		either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->

		<OutputType>Exe</OutputType>
		<RootNamespace>SmartpaMobiles.UI</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>SmartpaMobiles.UI</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.smartpamobiles.ui</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">14.2</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">13.1</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">30.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
		<AndroidSigningKeyStore>SmartpaMobiles.keystore</AndroidSigningKeyStore>
		<DefaultLanguage>el</DefaultLanguage>
		<PackageIcon>applogo.png</PackageIcon>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-android|AnyCPU'">
	  <PublishTrimmed>False</PublishTrimmed>
	  <RunAOTCompilation>False</RunAOTCompilation>
	  <AndroidPackageFormat>aab</AndroidPackageFormat>
	  <ApplicationTitle>SmartPa</ApplicationTitle>
	  <ApplicationId>com.intelsoft.gr.smartpa</ApplicationId>
	  <AndroidKeyStore>True</AndroidKeyStore>
	  <AndroidSigningStorePass>112325@A</AndroidSigningStorePass>
	  <AndroidSigningKeyAlias>smartpamobiles</AndroidSigningKeyAlias>
	  <AndroidSigningKeyPass>112325@A</AndroidSigningKeyPass>
	  <ApplicationDisplayVersion>1.11</ApplicationDisplayVersion>
	  <ApplicationVersion>111</ApplicationVersion>
	  <AndroidUseAapt2>True</AndroidUseAapt2>
	  <AndroidCreatePackagePerAbi>False</AndroidCreatePackagePerAbi>
	  <DebugSymbols>False</DebugSymbols>
	  <Optimize>True</Optimize>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net9.0-android|AnyCPU'">
	  <ApplicationTitle>SmartPa</ApplicationTitle>
	  <ApplicationId>com.intelsoft.gr.smartpa</ApplicationId>
	  <AndroidKeyStore>True</AndroidKeyStore>
	  <AndroidSigningStorePass>112325@A</AndroidSigningStorePass>
	  <AndroidSigningKeyAlias>smartpamobiles</AndroidSigningKeyAlias>
	  <AndroidSigningKeyPass>112325@A</AndroidSigningKeyPass>
	  <ApplicationDisplayVersion>1.11</ApplicationDisplayVersion>
	  <ApplicationVersion>111</ApplicationVersion>
	  <AndroidUseAapt2>True</AndroidUseAapt2>
	  <AndroidCreatePackagePerAbi>False</AndroidCreatePackagePerAbi>
	  <AndroidPackageFormat>aab</AndroidPackageFormat>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net9.0-ios|AnyCPU'">
	  <ApplicationTitle>SmartPa</ApplicationTitle>
	  <ApplicationId>com.intelsoft.gr.smartpa</ApplicationId>
	  <ApplicationDisplayVersion>1.11</ApplicationDisplayVersion>
	  <ApplicationVersion>111</ApplicationVersion>
	  <MtouchDebug>True</MtouchDebug>
	  <BuildIpa>False</BuildIpa>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net9.0-maccatalyst|AnyCPU'">
	  <ApplicationTitle>SmartPa</ApplicationTitle>
	  <ApplicationId>com.intelsoft.gr.smartpa</ApplicationId>
	  <ApplicationDisplayVersion>1.11</ApplicationDisplayVersion>
	  <ApplicationVersion>111</ApplicationVersion>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-ios|AnyCPU'">
	  <ApplicationTitle>SmartPa</ApplicationTitle>
	  <ApplicationId>com.intelsoft.gr.smartpa</ApplicationId>
	  <ApplicationDisplayVersion>1.11</ApplicationDisplayVersion>
	  <ApplicationVersion>111</ApplicationVersion>
	  <Optimize>True</Optimize>
	  <MtouchDebug>True</MtouchDebug>
	  <BuildIpa>False</BuildIpa>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-maccatalyst|AnyCPU'">
	  <ApplicationTitle>SmartPa</ApplicationTitle>
	  <ApplicationId>com.intelsoft.gr.smartpa</ApplicationId>
	  <ApplicationDisplayVersion>1.11</ApplicationDisplayVersion>
	  <ApplicationVersion>111</ApplicationVersion>
	  <Optimize>True</Optimize>
	</PropertyGroup>

	<PropertyGroup Condition="'$(TargetFramework)'=='net9.0-ios'">
	  <CodesignKey>Apple Development: Created via API (C36CCL87GN)</CodesignKey>
	  <CodesignProvision>VS: com.intelsoft.gr.smartpa Development</CodesignProvision>
	</PropertyGroup>

	

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.png" ForegroundFile="Resources\AppIcon\appiconfg.png" />
		<MauiSplashScreen Include="Resources\Images\applogo.png">
		  <BaseSize>128,128</BaseSize>
		</MauiSplashScreen>

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.png" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	

	<ItemGroup>
	  <MauiImage Remove="Resources\Images\applogo.png" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="appsettings.Development.json" />
	  <None Remove="appsettings.json" />
	  <None Remove="appsettings.Production.json" />
	  <None Remove="Resources\AppIcon\appiconfg.png" />
	  <None Remove="Resources\Images\add.png" />
	  <None Remove="Resources\Images\calendar.svg" />
	  <None Remove="Resources\Images\user.svg" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="appsettings.Production.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </EmbeddedResource>
	  <EmbeddedResource Include="appsettings.Development.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </EmbeddedResource>
	  <EmbeddedResource Include="appsettings.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="15.0.1" />
		<PackageReference Include="CommunityToolkit.Maui" Version="12.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.9" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.9" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.9" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.110" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.110" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.9" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.4" />
		<PackageReference Include="Refractored.MvvmHelpers" Version="1.6.2" />
		<PackageReference Include="Syncfusion.Maui.Inputs" Version="31.1.21" />
		<PackageReference Include="Syncfusion.Maui.Picker" Version="31.1.21" />
		<PackageReference Include="Syncfusion.Maui.Scheduler" Version="31.1.21" />
		<PackageReference Include="Syncfusion.Maui.Toolkit" Version="1.0.7" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\SmartpaMobiles.ApiShared\SmartpaMobiles.ApiShared.csproj" />
	  <ProjectReference Include="..\SmartpaMobiles.Data\SmartpaMobiles.Data.csproj" />
	  <MauiIcon Include="Resources\AppIcon\appiconfg.png" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Pages\ClientsPage.xaml.cs">
	    <DependentUpon>ClientsPage.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Pages\Resources\AppointmentPageResources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>AppointmentPageResources.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Pages\Resources\CalendarPageResources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>CalendarPageResources.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Pages\Resources\ClientPageResources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>ClientPageResources.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Pages\Resources\ClientsPageResources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>ClientsPageResources.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Pages\Resources\LoginPageResources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>LoginPageResources.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Properties\Resources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Resources.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Resources\GlobalResources.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>GlobalResources.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Resources\SfScheduler.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>SfScheduler.resx</DependentUpon>
	  </Compile>
	  <Compile Update="Resources\SfTimePicker.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>SfTimePicker.resx</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="Pages\Resources\AppointmentPageResources.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	    <LastGenOutput>AppointmentPageResources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Pages\Resources\CalendarPageResources.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	    <LastGenOutput>CalendarPageResources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Pages\Resources\ClientPageResources.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>ClientPageResources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Pages\Resources\ClientsPageResources.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	    <LastGenOutput>ClientsPageResources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Pages\Resources\LoginPageResources.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	    <LastGenOutput>LoginPageResources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Properties\Resources.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>Resources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Resources\GlobalResources.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	    <LastGenOutput>GlobalResources.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Resources\SfScheduler.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	    <LastGenOutput>SfScheduler.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Resources\SfScheduler.el-GR.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Resources\SfTimePicker.el-GR.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	  </EmbeddedResource>
	  <EmbeddedResource Update="Resources\SfTimePicker.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	    <LastGenOutput>SfTimePicker.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
	  <MauiXaml Update="Pages\AppointmentPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Pages\CalendarPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Pages\ClientPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Pages\ClientsPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Pages\HomePage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Pages\TabPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>

	<ItemGroup>
	  <None Update="Pages\TabPage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </None>
	  <None Update="Resources\Images\applogo.png">
	    <Pack>True</Pack>
	    <PackagePath>\</PackagePath>
	  </None>
	</ItemGroup>

</Project>
