﻿using AutoMapper;
using AutoMapper;
using SmartpaMobiles.Business.Request;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using SmartpaMobiles.Business.Request;

namespace SmartpaMobiles.Business
{
    public class AppointmentsBusiness : IAppointmentsBusiness
    {
        private SmartpaMobiles.Data.Models.SmartpaContext dbContext;
        //private Microsoft.Extensions.Hosting.IHostEnvironment hostEnvironment;
        //private IConfiguration configuration;
        private IMapper mapper;
        private ILogger logger;

        public AppointmentsBusiness(SmartpaMobiles.Data.Models.SmartpaContext dbContext, IMapper mapper, ILogger<AppointmentsBusiness> logger)
        {
            this.dbContext = dbContext;
            //this.hostEnvironment = hostEnv;
            //this.configuration = configuration;
            this.mapper = mapper;
            this.logger = logger;
        }

        public async Task<List<SmartpaMobiles.Data.Models.Appointment>> GetAppointmentsOfOfficer(DateTime startDate, DateTime endDate, int officerId)
        {
            try
            {
                //Validation

                //Query
                IQueryable<SmartpaMobiles.Data.Models.Appointment> query = this.dbContext.Appointments.AsNoTrackingWithIdentityResolution().Include(x => x.Participations).ThenInclude(x => x.Client).AsQueryable();
                query = query.Where(a => a.OfficerId == officerId && a.StartDate >= startDate && a.Canceled == false && a.EndDate <= endDate).OrderByDescending(a => a.StartDate).OrderByDescending(a => a.AllDay);

                //Διαβάζει τα δεδομένα.
                List<SmartpaMobiles.Data.Models.Appointment> appoitments = await query.ToListAsync();

                //Response
                return appoitments;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "OfficerId=" + officerId.ToString() + "StartDate=" + startDate.ToString() + "EndDate=" + endDate.ToString() });
                throw;
            }
        }


        public async Task<Data.Models.Appointment?> GetAppointment(int officerId, int appointmentId)
        {
            try
            {
                //Query
                IQueryable<Appointment> query = dbContext.Appointments.AsNoTrackingWithIdentityResolution().Include(x => x.Participations).ThenInclude(x => x.Client).AsQueryable();
                Appointment? appointment = query.Where(c => c.AppointmentId == appointmentId /*&& c.OfficerId == officerId*/).FirstOrDefault();

                //Καθαρίζει επιπλέον δεδομένα που δεν χρειάζονται.
                if (appointment != null)
                {
                    foreach (Participation p in appointment.Participations)
                    {
                        if (p.Client != null)
                        {
                            p.Client!.Participations = new List<Participation>();
                        }
                    }
                }

                return appointment;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message, new object[] { "AppointmentId=" + appointmentId.ToString() });
                throw;
            }
        }

        public async Task CreateOrUpdateAppointment(Appointment appointment)
        {
            try
            {
                //Validation
                if (appointment == null)
                {
                    throw new Exception(Resources.GlobalResources.InvalidDataMessage);
                }

                List<ValidationResult> validationResults = new List<ValidationResult>();
                ValidationContext validationContext = new ValidationContext(appointment);
                if (Validator.TryValidateObject(appointment, validationContext, validationResults, true) == false)
                {
                    string validationErrors = "";
                    foreach (ValidationResult compValidationResult in validationResults)
                    {
                        //foreach (ValidationResult validationResult in compValidationResult.Results)
                        {
                            validationErrors += compValidationResult.ErrorMessage + ". ";
                        }
                    }
                    throw new ApplicationException(validationErrors);
                }

                if (await this.CheckForClosedOfficeOfOfficer(appointment.OfficerId, appointment.AppointmentId, appointment.StartDate.Value, appointment.EndDate.Value) == true)
                {
                    throw new ApplicationException(Resources.ValidationResources.OfficeClosedMessage);
                }

                ////Αν υπάρχουν άλλα ραντεβού που να συμπίπτουν με το συγκεκριμένο ραντεβού.
                //if (await this.GetConflictAppointmentsOfOfficer(appointment.OfficerId, appointment.AppointmentId, appointment.StartDate.Value, appointment.EndDate.Value) == true)
                //{
                //    throw new ApplicationException(Resources.ValidationResources.AppointmentConflictOnSaveMessage);
                //}

                //Query
                this.dbContext.Attach(appointment);
                await this.dbContext.SaveChangesAsync();

                //Response
            }
            catch (ApplicationException ex)
            {
                throw;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, new object[] { appointment.AppointmentId });
                throw;
            }
        }

        public async Task DeleteAppointment(int appointmentId)
        {
            Appointment? appointment = await this.dbContext.Appointments.Where(x => x.AppointmentId == appointmentId).FirstAsync();
            if (appointment != null)
            {
                appointment.ObjectState = ObjectState.Deleted;
                this.dbContext.Attach(appointment);
                this.dbContext.SaveChanges();
            }
        }

        public async Task<bool> CheckForClosedOfficeOfOfficer(int officerId, int appointmentId, DateTime startDate, DateTime endDate)
        {
            int conflictsCount = await this.dbContext.Appointments.Where(a => a.AppointmentType == "Event" && a.Block == true && a.OfficerId == officerId
                && a.AllDay == false && a.AppointmentId != appointmentId && ((a.StartDate <= startDate && a.EndDate >= startDate) || (a.StartDate <= endDate && a.EndDate >= endDate) || (a.StartDate >= startDate && a.EndDate <= endDate)
                || (a.StartDate >= startDate && a.EndDate <= endDate))).OrderByDescending(a => a.StartDate).CountAsync();
            return conflictsCount > 0 ? true : false;
        }

        public async Task<bool> GetConflictAppointmentsOfOfficer(int officerId, int appointmentId, DateTime startDate, DateTime endDate)
        {
            //int conflictsCount1 = await this.dbContext.Appointments.Where(a => a.Canceled == false && a.OfficerId == officerId && a.AllDay == false && a.AppointmentId != appointmentId
            //    && ((startDate >= a.StartDate && startDate <= a.EndDate) || (endDate >= a.StartDate && endDate <= a.EndDate) || (startDate <= a.StartDate && endDate >= a.EndDate))).OrderByDescending(a => a.StartDate).CountAsync();

            int conflictsCount = await this.dbContext.Appointments.Where(a => a.Canceled == false && a.OfficerId == officerId && a.AllDay == false && a.AppointmentId != appointmentId
                && ((a.StartDate >= startDate && a.StartDate <= endDate) || (a.EndDate >= startDate && a.EndDate <= endDate) || (a.StartDate <= startDate && a.EndDate >= endDate))).OrderByDescending(a => a.StartDate).CountAsync();

            return conflictsCount > 0 ? true : false;
        }

        public async Task<List<SmartpaMobiles.Data.Models.AppointmentService>> GetAllAppointmentServices(int officerId)
        {
            try
            {
                //Validation

                //Query
                IQueryable<SmartpaMobiles.Data.Models.AppointmentService> query = this.dbContext.AppointmentServices.AsQueryable();

                //Διαβάζει τα δεδομένα.
                List<SmartpaMobiles.Data.Models.AppointmentService> appointmentServices = await query.Where(x => x.OfficerId == officerId).AsNoTrackingWithIdentityResolution().ToListAsync();

                //Response
                return appointmentServices;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                throw;
            }
        }
    }
}
