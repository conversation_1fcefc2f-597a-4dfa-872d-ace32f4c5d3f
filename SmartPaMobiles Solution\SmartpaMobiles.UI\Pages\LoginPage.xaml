<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:resources="clr-namespace:SmartpaMobiles.UI.Pages.Resources"
             x:Class="SmartpaMobiles.UI.Pages.LoginPage"
             Title="">
    
    <VerticalStackLayout Padding="30,30,30,30" Spacing="20">

        <Image HeightRequest="200" Margin="0,30,0,0" Source="applogo.png"/>
        
        <Label Text="{x:Static resources:LoginPageResources.LogonMessage}" VerticalOptions="Center" HorizontalOptions="Center" />

        <Entry x:Name="usernameEntry" MaxLength="50" Placeholder="username" />
        
        <Entry x:Name="passwordEntry" IsPassword="True" MaxLength="50" Placeholder="password" />

        <ActivityIndicator x:Name="activityIndicator" HeightRequest="40"/>
        <Button x:Name="loginBtn" Margin="50,0,50,0" Text="{x:Static resources:LoginPageResources.LoginBtn_Text}" Clicked="loginBtnClicked"></Button>

        
    </VerticalStackLayout>

</ContentPage>