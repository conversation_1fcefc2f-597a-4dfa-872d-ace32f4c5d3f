﻿using SmartpaMobiles.ApiShared;
using Newtonsoft.Json;
using System.Text;

namespace SmartpaMobiles.UI.WebApiClient
{
    public class LogWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";

        public LogWebApiClient(HttpClient httpClient)
        {
            this.httpClient = httpClient;
            if (this.httpClient.Timeout.TotalSeconds == 100)
            {
                this.httpClient.Timeout = new TimeSpan(0, 0, 30);
            }
        }

        public async Task LogException(Exception ex)
        {
            string requestUri = this.httpClient.BaseAddress + "api/" + apiVersion + "/" + "Log/LogException";
            requestUri += "?Product=SmartpaMobiles.UI";

            string contactJson = JsonConvert.SerializeObject(ex);  //Μετατρέπουμε το Exception object σε json.

            //Ετοιμάζουμε το HttpContext με τα json data.
            HttpContent httpContext = new StringContent(contactJson, Encoding.UTF8, "application/json");

            //Εκτελούμε το POST request.
            HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse

            //string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
            //ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString);  //Μετατρέπουμε το δικό μας response σε object
        }
    }
}
