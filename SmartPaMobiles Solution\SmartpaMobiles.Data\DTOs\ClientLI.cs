﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.Data.DTOs
{
    public class ClientLI
    {
        public int ClientId { get; set; }
        public string FirstLastName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Occupation { get; set; } = string.Empty;

        public string Initials
        {
            get
            {
                string fixedFirstLastName = FirstLastName.Replace("  ", " ").Trim();
                if (fixedFirstLastName.Length > 0 )
                {
                     return fixedFirstLastName.Split(' ')[0].Substring(0, 1).ToUpper() + (FirstLastName.Split(' ').Length > 1 ? (FirstLastName.Split(' ')[1].Length > 0 ? FirstLastName.Split(' ')[1][0].ToString().ToUpper() : string.Empty) : "");
                }
                else
                {
                    return "";
                }
            }
        }
    }
}
