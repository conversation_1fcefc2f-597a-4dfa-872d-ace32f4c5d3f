﻿using SmartpaMobiles.Business;

namespace SmartpaMobiles.Api
{
    public class WebApiCallerInfo : IWebApiCallerInfo
    {
        public int? OfficerId { get; private set; }
        public int? UserId { get; private set; }

        public WebApiCallerInfo()
        {
        }

        //public async Task<bool> ValidateCallerInfo(Guid tenantId, Guid userId)
        //{
        //    User? user = await this.usersBusiness.GetUser(userId);  //Αν ο χρήστης υπάρχει
        //    //TODO: να μπει εδώ έλεγχος αν η συνδρομή έχει λήξει, επειδή μπορεί το token να κρατάει πολύ καιρό.
        //    if (user != null && user.TenantId == tenantId)
        //    {
        //        TenantId = user.TenantId;
        //        UserId = user.UserId;
        //        return true;
        //    }
        //    else
        //    {
        //        throw new Exception("Tenant invalid");
        //    }
        //}
    }
}
