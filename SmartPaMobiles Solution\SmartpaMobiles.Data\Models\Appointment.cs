﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartpaMobiles.Data.Models;

public partial class Appointment : IObjectState
{
    private DateTime? startDateTime;
    private DateTime? endDateTime;
    //private DateOnly? startDate;
    //private TimeOnly? startTime = TimeOnly.MinValue;
    //private DateOnly? endDate;
    //private TimeOnly? endTime = TimeOnly.MinValue;
    public Appointment()
    {
        this.ObjectState = ObjectState.Unchanged;
        this.Participations = new List<Participation>();
    }

    public int AppointmentId { get; set; }

    public int OfficerId { get; set; }

    public int? Type { get; set; }

    //public DateTime? StartDate
    //{
    //    get
    //    {
    //        return startDate?.ToDateTime(this.startTime ?? TimeOnly.MinValue);
    //    }
    //    set
    //    {
    //        //DateTime? initialStartDate = (DateTime?)(startDate?.ToDateTime(this.startTime ?? TimeOnly.MinValue));
    //        //TimeSpan duration = EndDate?.Subtract(initialStartDate ?? DateTime.Now).Duration() ?? TimeSpan.Zero;

    //        startDate = value != null ? DateOnly.FromDateTime(value.Value) : null;
    //        startTime = value != null ? TimeOnly.FromTimeSpan(value.Value.TimeOfDay) : null;

    //        //endDate = startDate;  //βάζουμε το ίδιο endDate με το startDate ώστε να πετύχουμε να παραμείνει η διάρκεια του ραντεβού και να μην αλλάξει η ημέρα λήξης.

    //        //if (initialStartDate != null && duration != TimeSpan.Zero)
    //        //{
    //        //    EndDate = initialStartDate.Value.Date + duration;
    //        //}
    //    }
    //}

    public DateTime? StartDate
    {
        get
        {
            return this.startDateTime;

        }
        set
        {
            this.startDateTime = value;
        }
    }

    //public DateTime? EndDate
    //{
    //    get
    //    {
    //        return endDate?.ToDateTime(this.endTime ?? TimeOnly.MinValue);
    //    }
    //    set
    //    {
    //        //DateTime? initialEndDate = (DateTime?)(endDate?.ToDateTime(this.endTime ?? TimeOnly.MinValue));

    //        endDate = value != null ? DateOnly.FromDateTime(value.Value) : null;
    //        endTime = value != null ? TimeOnly.FromTimeSpan(value.Value.TimeOfDay) : null;
    //    }
    //}

    public DateTime? EndDate
    {
        get
        {
            return this.endDateTime;

        }
        set
        {
            this.endDateTime = value;
        }
    }

    [NotMapped]
    public DateTime? StartDateOnly
    {
        get
        {
            return this.startDateTime?.Date;
        }
        set
        {
            this.startDateTime = value?.Date.Add(this.startDateTime?.TimeOfDay ?? TimeSpan.Zero);
        }
    }

    [NotMapped]
    public DateTime? EndDateOnly
    {
        get
        {
            return this.endDateTime?.Date;
        }
        set
        {
            this.endDateTime = value?.Date.Add(this.endDateTime?.TimeOfDay ?? TimeSpan.Zero);
        }
    }

    //[NotMapped]
    //public TimeSpan? StartTime
    //{
    //    get
    //    {
    //        return this.startTime?.ToTimeSpan();
    //    }
    //    set
    //    {
    //        this.startTime = value == null ? TimeOnly.MinValue : TimeOnly.FromTimeSpan(value.Value);
    //    }
    //}
    [NotMapped]
    public TimeSpan? StartTimeOnly
    {
        get
        {
            return this.startDateTime?.TimeOfDay;
        }
        set
        {
            this.startDateTime = this.startDateTime?.Date.Add(value ?? TimeSpan.Zero);
        }
    }

    //[NotMapped]
    //public TimeSpan? EndTime
    //{
    //    get
    //    {
    //        return this.endTime?.ToTimeSpan();
    //    }
    //    set
    //    {
    //        this.endTime = value == null ? TimeOnly.MinValue : TimeOnly.FromTimeSpan(value.Value);
    //    }
    //}

    [NotMapped]
    public TimeSpan? EndTimeOnly
    {
        get
        {
            return this.endDateTime?.TimeOfDay;
        }
        set
        {
            this.endDateTime = this.endDateTime?.Date.Add(value ?? TimeSpan.Zero);
        }
    }

    public bool? AllDay { get; set; }

    public string? Subject { get; set; } = string.Empty;

    public string? Location { get; set; } = string.Empty;

    public string? Description { get; set; } = string.Empty;

    public int? Status { get; set; }

    public int? Label { get; set; }

    public int? ResourceId { get; set; }

    public string? ResourceIds { get; set; }

    public string? ReminderInfo { get; set; }

    public string? RecurrenceInfo { get; set; }

    public string? Notes { get; set; } = string.Empty;

    public int? CreatedByUserId { get; set; }

    public DateTime? CreateDate { get; set; }

    public bool Canceled { get; set; }

    /// <summary>
    /// Πιθανές τιμές: Appointment=κανονικό ραντεβού (default), OfficeClosed=Ιατρείο κλειστό,
    /// </summary>
    public string AppointmentType { get; set; } = null!;

    public bool Block { get; set; }

    public bool IsRemote { get; set; }

    public string RemoteMeetingUrl { get; set; } = string.Empty;

    public bool ClientEmailNotificationSent { get; set; }

    public bool SmsReminderEnabled { get; set; }

    public bool SmsReminderSent { get; set; }

    public bool? ClientAttended { get; set; }

    public User? CreatedByUser { get; set; }

    public Officer? Officer { get; set; } = null!;

    public ICollection<Participation> Participations { get; set; } = new List<Participation>();

    [NotMapped]
    public string Summary
    {
        get
        {
            string summary = string.Empty;

            //Εφόσον δεν είναι ολοήμερο
            if (!this.AllDay.HasValue || this.AllDay.Value == false)
            {
                //if (this.StartDate.HasValue && this.EndDate.HasValue)
                //{
                //    summary = this.StartDate.Value.ToString("HH:mm") + "-" + this.EndDate.Value.ToString("HH:mm");
                //}

                //Αν τα Participations υπάρχουν
                if (this.Participations?.FirstOrDefault() != null)
                {
                    if (this.Participations.FirstOrDefault()?.Client != null)
                    {
                        summary = (summary!.Length > 0 ? (summary + " - ") : "") + this.Participations.FirstOrDefault()?.Client?.FirstLastName;
                    }
                    if (string.IsNullOrEmpty(this.Participations.FirstOrDefault()?.Client?.Amka) == false)
                    {
                        summary = (summary!.Length > 0 ? (summary + " - ") : "") + this.Participations.FirstOrDefault().Client.Amka;
                    }
                }

                if (this.Subject != null)
                {
                    summary = (summary!.Length > 0 ? (summary + " - ") : "") + (this.Subject.Length > 200 ? this.Subject.Substring(0, 200) : this.Subject) + (this.Subject.Length > 200 ? "..." : string.Empty);
                }

                //Αν τα Participations υπάρχουν
                if (this.Participations?.FirstOrDefault() != null)
                {
                    if (this.Participations?.FirstOrDefault()?.Client != null)
                    {
                        if (string.IsNullOrEmpty(this.Participations.FirstOrDefault()?.Client?.Mobile1) == false)
                        {
                            summary = (summary!.Length > 0 ? (summary + " - ") : "") + this.Participations.FirstOrDefault()?.Client.Mobile1;
                        }
                        if (string.IsNullOrEmpty(this.Participations.FirstOrDefault()?.Client?.Phone1) == false)
                        {
                            summary = (summary!.Length > 0 ? (summary + " - ") : "") + this.Participations.FirstOrDefault().Client.Phone1;
                        }
                    }
                }
            }
            else  //Αν είναι ολοήμερο
            {
                if (this.Subject != null)
                {
                    summary = (this.Subject.Length > 200 ? this.Subject.Substring(0, 200) : this.Subject) + (this.Subject.Length > 200 ? "..." : "");
                }
            }

            return summary;

        }
    }

    [NotMapped]
    public ObjectState ObjectState { get; set; }
}