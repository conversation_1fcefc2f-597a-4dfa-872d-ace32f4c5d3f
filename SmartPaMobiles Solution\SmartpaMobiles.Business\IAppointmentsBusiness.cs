﻿using SmartpaMobiles.Business.Request;
using SmartpaMobiles.Business.Response;
using SmartpaMobiles.Data.DTOs;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Shared;

namespace SmartpaMobiles.Business
{
    public interface IAppointmentsBusiness
    {
        Task<List<SmartpaMobiles.Data.Models.Appointment>> GetAppointmentsOfOfficer(DateTime startDate, DateTime endDate, int officerId);
        Task<SmartpaMobiles.Data.Models.Appointment?> GetAppointment(int officerId, int appointmentId);
        Task CreateOrUpdateAppointment(Appointment appointment);
        Task DeleteAppointment(int appointmentId);
        Task<bool> GetConflictAppointmentsOfOfficer(int officerId, int appointmentId, DateTime startDate, DateTime endDate);
        Task<List<AppointmentService>> GetAllAppointmentServices(int officerId);
    }
}