﻿using Microsoft.Extensions.Primitives;
using SmartpaMobiles.Api;
using System.Security.Claims;
using System.Text.Json;

namespace SmartpaMobiles.Api
{
    internal class WebApiCallerMiddleware
    {
        private readonly RequestDelegate _next;
        public WebApiCallerMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        // Get Tenant Id from incoming requests 
        public async Task InvokeAsync(HttpContext context, IWebApiCallerInfo currentTenantService)
        {
            context.Request.Headers.TryGetValue("Authorization", out StringValues strValues); // TenantId from incoming request header
            if (strValues.Count > 0)
            {
                string token = strValues[0]!.Replace("brearer", "").Trim();
                SmartpaMobiles.Shared.Authorization.JwtHelper.ParseClaimsFromJwt(token, out Dictionary<string, Claim> claims);
                //string tenantId = claims["TenantId"].Value;
                string userId = claims["UserId"].Value;
                //await currentTenantService.ValidateCallerInfo(Guid.Parse(tenantId), Guid.Parse(userId));
            }

            await _next(context);
        }
    }
}
