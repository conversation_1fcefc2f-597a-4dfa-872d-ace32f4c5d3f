﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.Data.Models
{
    public partial class Appointment
    {
        public static List<AppointmentLabelColor> LabelColors
        {
            get
            {
                List<AppointmentLabelColor> list = new List<AppointmentLabelColor>();
                list.Add(new AppointmentLabelColor() { Id = 0, Title = "", Color = ColorTranslator.FromHtml("#fff") });
                list.Add(new AppointmentLabelColor() { Id = 1, Title = "Αποτελέσματα εξετάσεων", Color = ColorTranslator.FromHtml("#ffffc2be") });
                list.Add(new AppointmentLabelColor() { Id = 2, Title = "Ασφάλειες", Color = ColorTranslator.FromHtml("#ffa8d5ff") });
                list.Add(new AppointmentLabelColor() { Id = 3, Title = "Ε.Ο.Π.Υ.Υ", Color = ColorTranslator.FromHtml("#ffc1f49c") });
                list.Add(new AppointmentLabelColor() { Id = 4, Title = "", Color = ColorTranslator.FromHtml("#fff3e4c7") });
                list.Add(new AppointmentLabelColor() { Id = 5, Title = "Σημειώσεις μέρας", Color = ColorTranslator.FromHtml("#fff4ce93") });
                list.Add(new AppointmentLabelColor() { Id = 6, Title = "", Color = ColorTranslator.FromHtml("#ffc7f4ff") });
                list.Add(new AppointmentLabelColor() { Id = 7, Title = "Holter", Color = ColorTranslator.FromHtml("#ffcfdb98") });
                list.Add(new AppointmentLabelColor() { Id = 8, Title = "Άυλες", Color = ColorTranslator.FromHtml("#ffe0cfe9") });
                list.Add(new AppointmentLabelColor() { Id = 9, Title = "Ι.Ε.", Color = ColorTranslator.FromHtml("#ff8de9df") });
                list.Add(new AppointmentLabelColor() { Id = 10, Title = "Ιδιωτικό ραντεβού (εξέταση/συνταγογράφηση)", Color = ColorTranslator.FromHtml("#fffff7a5") });
                list.Add(new AppointmentLabelColor() { Id = 11, Title = "Νέος ασθενής", Color = ColorTranslator.FromHtml("#FF887C") });
                list.Add(new AppointmentLabelColor() { Id = 12, Title = "Κατ' Οίκον", Color = ColorTranslator.FromHtml("#c2c2c2") });
               

                return list;
            }
        }

    }
}
