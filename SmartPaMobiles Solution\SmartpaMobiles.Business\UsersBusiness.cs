﻿using AutoMapper;
using SmartpaMobiles.Business.Request;
using SmartpaMobiles.Business.Response;
using SmartpaMobiles.Shared;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using static System.Runtime.InteropServices.JavaScript.JSType;
using SmartpaMobiles.Business;
using SmartpaMobiles.Data.Models;

namespace SmartpaMobiles.Business
{
    public class UsersBusiness : IUsersBusiness
    {
        private SmartpaMobiles.Data.Models.SmartpaContext dbContext;
        private IMapper mapper;
        private ILogger logger;

        public UsersBusiness(SmartpaMobiles.Data.Models.SmartpaContext dbContext, IMapper mapper, ILogger logger)
        {
            this.dbContext = dbContext;
            this.mapper = mapper;
            this.logger = logger;
        }



        public async Task<Data.Models.User?> GetUser(string username, string password)
        {
            try
            {
                //Query
                IQueryable<SmartpaMobiles.Data.Models.User> query = this.dbContext.Users.AsQueryable();
                query = query.Include(x => x.Officer).Include(x => x.Role).Where(x => x.Username == username && x.Password == password);
                Data.Models.User? user = await query.FirstOrDefaultAsync();

                //Result
                return user;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }

        public async Task<Data.Models.User?> GetUser(int userId)
        {
            try
            {
                //Query
                IQueryable<SmartpaMobiles.Data.Models.User> query = this.dbContext.Users.Include(x => x.Role).Where(x => x.UserId == userId);
                Data.Models.User? user = await query.FirstOrDefaultAsync();

                //Result
                return user;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }


        public async Task<bool> CheckUsernamePasswordExists(string username, string password)
        {
            try
            {
                //Query
                IQueryable<SmartpaMobiles.Data.Models.User> query = this.dbContext.Users.AsQueryable();
                query = query.Where(x => x.Username == username && x.Password == password);
                int count = await query.CountAsync();

                return count > 0;
            }
            catch (Exception ex)
            {
                logger.Log(LogLevel.Error, ex.Message, new object[] { ex });
                throw;
            }
        }
    }
}
