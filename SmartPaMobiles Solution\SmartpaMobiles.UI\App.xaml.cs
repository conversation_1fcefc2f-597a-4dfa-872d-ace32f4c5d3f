﻿using Syncfusion.Maui.Scheduler;
using System.Globalization;
using System.Resources;

namespace SmartpaMobiles.UI
{
    public partial class App : Application
    {
        public App()
        {
            InitializeComponent();

            CultureInfo.CurrentUICulture = new CultureInfo("el-GR");
            SfSchedulerResources.ResourceManager = new ResourceManager("SmartpaMobiles.UI.Resources.SfScheduler", Application.Current.GetType().Assembly);


            MainPage = new AppShell();
        }
    }
}
