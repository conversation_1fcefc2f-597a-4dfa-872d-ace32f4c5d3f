﻿using SmartpaMobiles.Shared.Authorization;
using SmartpaMobiles.ApiShared.Response;
using Newtonsoft.Json;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.ApiShared.Request;
using System.Text;

namespace SmartpaMobiles.UI.WebApiClient
{
    public class AuthenticationWebApiClient
    {
        private HttpClient httpClient;
        //private readonly IHttpService httpService;

        public AuthenticationWebApiClient(HttpClient httpClient)
        {
            this.httpClient = httpClient;
        }

        public async Task<LoginResponse> Login(string username, string password)
        {
            string requestUri = this.httpClient.BaseAddress + @"Authentication/Login";
            //requestUri += "?Username=" + username + "&Password=" + password;

            LoginParameters loginParameters = new LoginParameters()
            {
                Username = username,
                Password = password
            };
            string credentialsJson = System.Text.Json.JsonSerializer.Serialize(loginParameters);  //Μετατρέπουμε το Appointment object σε json.

            //Ετοιμάζουμε το HttpContext με τα json data.
            HttpContent httpContext = new StringContent(credentialsJson, Encoding.UTF8, "application/json");


            // string responseString = await this.httpClient.GetStringAsync(requestUri);
            //ApiResponse<LoginResponse>? response = JsonConvert.DeserializeObject<ApiResponse<LoginResponse>>(responseString);
            HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);
            string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
            ApiResponse<LoginResponse>? response = JsonConvert.DeserializeObject<ApiResponse<LoginResponse>>(responseString);  //Μετατρέπουμε το δικό μας response σε object
            
            if (response!.ResultCode == ApiResponseResultCode.Ok)
            {
                return response.ResponseContent;
            }
            else if (response.ResultCode == ApiResponseResultCode.Exception)
            {
                throw new ApplicationException(response.ExceptionMessage ?? "");
            }

            return null;
        }

        public async Task<UserToken> RenewToken()
        {
            string requestUri = this.httpClient.BaseAddress + "RenewToken" ;  

            string responseString = await this.httpClient.GetStringAsync(requestUri);
            ApiResponse<UserToken>? response = JsonConvert.DeserializeObject<ApiResponse<UserToken>>(responseString);
            if (response!.ResultCode == ApiResponseResultCode.Ok)
            {
                return response.ResponseContent;
            }
            else if (response.ResultCode == ApiResponseResultCode.Exception)
            {
                if (response.ExceptionMessageForUser != null)
                {
                    throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                }
                else
                {
                    throw new Exception(response.ExceptionMessage ?? "");
                }
            }

            return null;

        }
    }
}
