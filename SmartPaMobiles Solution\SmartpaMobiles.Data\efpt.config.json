﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "SmartpaContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": false,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Models",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "SmartpaMobiles.Data",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[Appointments]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[AppointmentServices]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[CExceptions]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ClientFiles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Clients]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DoctorSpecial<PERSON>]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Expenses]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[InsuranceAgencies]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Invoices]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[InvoiceTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Officers]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[OfficerSettings]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Participations]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[PaymentTypes]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[RestrictedOfficers]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Roles]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Settings]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[Users]",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": true,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": true,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false
}