﻿using SmartpaMobiles.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.Business.Request
{
    public class ReadPagedDataParameters
    {
        public Int64 OfficerId { get; set; }  
        public string Filter { get; set; } = "";
        public int PageIndex { get; set; } = 0;
        public int PageSize { get; set; } = 10;
        public Dictionary<string, SortOrder> SortColumns { get; set; } = new Dictionary<string, SortOrder>();
    }
}
