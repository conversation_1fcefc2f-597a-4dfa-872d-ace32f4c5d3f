﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace SmartpaMobiles.Data.Models;

public partial class Officer
{
    public int OfficerId { get; set; }

    public byte[] RowVersion { get; set; } = null!;

    public int? DoctorSpecialtyId { get; set; }

    public string Address { get; set; } = null!;

    public string City { get; set; } = null!;

    public string Region { get; set; } = null!;

    public string PostalCode { get; set; } = null!;

    public string Afm { get; set; } = null!;

    public string Doy { get; set; } = null!;

    public string Announcements { get; set; } = null!;

    public string SecretaryNotes { get; set; } = null!;

    public string OfficerNotes { get; set; } = null!;

    public bool ShowInSmartDoctors { get; set; }

    public string Ibans { get; set; } = null!;

    public string Resume { get; set; } = null!;

    public ICollection<AppointmentService> AppointmentServices { get; set; } = new List<AppointmentService>();

    public ICollection<Appointment> Appointments { get; set; } = new List<Appointment>();

    public ICollection<Client> Clients { get; set; } = new List<Client>();

    public DoctorSpecialty? DoctorSpecialty { get; set; }

    public ICollection<Expense> Expenses { get; set; } = new List<Expense>();

    public ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();

    public User OfficerNavigation { get; set; } = null!;

    public OfficerSetting? OfficerSetting { get; set; }

    public ICollection<RestrictedOfficer> RestrictedOfficers { get; set; } = new List<RestrictedOfficer>();
}