﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.UI
{
    internal static class ColorGenerator
    {
        private static Random random = new Random((int)DateTime.UtcNow.Ticks);

        public static Color RandomColor
        {
            get
            {
                return GetRandomColor();
            }
        }

        public static Color GetRandomColor()
        {
            var color = Color.FromRgb(random.Next(200, 255) , random.Next(150, 255), random.Next(150, 255));
            //lock (lockObject)
            //{
            //    if (currentColors.Contains(color, new ColorEqualityComparer()))
            //        return GetRandomColor();
            //    else
            //    {
            //        currentColors.Add(color);
            //        return color;
            //    }
            //}
            return color;
        }
    }
}
