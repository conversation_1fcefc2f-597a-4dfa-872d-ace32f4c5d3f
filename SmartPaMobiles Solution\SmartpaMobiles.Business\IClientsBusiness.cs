﻿using SmartpaMobiles.Business.Request;
using SmartpaMobiles.Business.Response;
using SmartpaMobiles.Data.DTOs;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Shared;

namespace SmartpaMobiles.Business
{
    public interface IClientsBusiness
    {
        Task<PagedData<List<SmartpaMobiles.Data.DTOs.ClientView>>> GetClients(ReadPagedDataParameters parameters);
        Task<SmartpaMobiles.Data.Models.Client?> GetClient(int clientId);
        Task<int> CreateOrUpdateClient(Client client);
        Task DeleteClient(int clientId);
        Task<List<ClientLI>> GetClientsLI(int officerId);
    }
}