﻿using SmartpaMobiles.ApiShared;
using SmartpaMobiles.Shared;
using System.Text;
using Newtonsoft.Json;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Data.DTOs;
using SmartpaMobiles.ApiShared.Request;
using SmartpaMobiles.ApiShared.Response;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using SmartpaMobiles.Shared.Authorization;


namespace SmartpaMobiles.UI.WebApiClient
{
    public class InsuranceAgenciesWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<InsuranceAgenciesWebApiClient> logger;

        public InsuranceAgenciesWebApiClient(HttpClient httpClient, ILogger<InsuranceAgenciesWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        //public async Task<List<SmartpaMobiles.Data.Models.InsuranceAgency>> GetAll(int officerId)
        //{
        //    try
        //    {
        //        string requestUri = httpClient.BaseAddress + apiVersion + "/" + "insurance-agencies";

                
        //        //Ετοιμάζουμε το HttpContext με τα json data.
        //        HttpContent httpContent = new StringContent(getEntitiesParamsJson, Encoding.UTF8, "application/json");
               
        //        string urlParameters = UrlHelpers.ToQueryString(requestDataParams, "&");
        //        requestUri = requestUri + "?" + urlParameters;

        //        //Εκτελούμε το request.
        //        //this.httpClient.DefaultRequestHeaders.Authorization= new AuthenticationHeaderValue("Bearer", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOiIyY2FhYmI1NC1mZDM4LTRhY2MtOTMyYS1kNWM3Yjk0ZmRkMmYiLCJVc2VybmFtZSI6IjMzIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZSI6IlAgTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IlN1cGVyQWRtaW4iLCJUZW5hbnRJZCI6IjNmYTg1ZjY0LTU3MTctNDU2Mi1iM2ZjLTJjOTYzZjY2YWZhNiIsImV4cCI6MTc0NzA0Mjk2Mn0.ZqUnCMKdLJ37E2PBZJJVTaFXpVBa5skxbHhBY18wTjs");
        //        HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET           
        //        //HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse

        //        string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
        //        ApiResponse<PagedData<List<Data.DTOs.ClientView>>> response = JsonConvert.DeserializeObject<ApiResponse<PagedData<List<Data.DTOs.ClientView>>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object
                
        //        if (response.ResultCode == ApiResponseResultCode.Ok)
        //        {
        //            return response.ResponseContent!; 
        //        }
        //        else if (response.ResultCode == ApiResponseResultCode.Exception)
        //        {
        //            throw new ApplicationException(response.ExceptionMessage ?? "");
        //        }
                
        //        return new List<Data.Models.InsuranceAgency> { Data = null, DataTotalCount = 0 };
        //    }
        //    catch (Exception ex)
        //    {
        //        this.logger.LogError(ex, "Error in GetClients({@officerId},{fiter},{@pageIndex}, {@pageSize})", officerId, filter, pageIndex, pageSize);
        //        throw;
        //    }
        //}

        public async Task<List<SmartpaMobiles.Data.Models.InsuranceAgency>> GetAll()
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/" + "insurance-agencies";

                //Εκτελούμε το request.
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Models.InsuranceAgency>> response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Models.InsuranceAgency>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    throw new ApplicationException(response.ExceptionMessage ?? "");
                }

                return new List<Data.Models.InsuranceAgency>();
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex.Message, null);
                throw;
            }
        }

    }
}
