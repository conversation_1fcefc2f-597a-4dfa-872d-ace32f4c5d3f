﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace SmartpaMobiles.Data.Models;

public partial class Invoice
{
    public int InvoiceId { get; set; }

    public int SerialNumber { get; set; }

    public int OfficerId { get; set; }

    public int ClientId { get; set; }

    public DateTime InvoiceDate { get; set; }

    public string Description { get; set; } = null!;

    public int PaymentTypeId { get; set; }

    public string Notes { get; set; } = null!;

    public int InvoiceTypeId { get; set; }

    public bool Payed { get; set; }

    public decimal TotalAmount { get; set; }

    public decimal Amount { get; set; }

    public decimal VatPercentage { get; set; }

    public decimal VatAmount { get; set; }

    public Client Client { get; set; } = null!;

    public InvoiceType InvoiceType { get; set; } = null!;

    public Officer Officer { get; set; } = null!;

    public PaymentType PaymentType { get; set; } = null!;
}