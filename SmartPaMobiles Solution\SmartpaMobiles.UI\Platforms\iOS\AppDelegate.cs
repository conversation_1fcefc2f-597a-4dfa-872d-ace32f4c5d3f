﻿using Foundation;
using System.Globalization;
using UIKit;

namespace SmartpaMobiles.UI
{
    [Register("AppDelegate")]
    public class AppDelegate : MauiUIApplicationDelegate
    {
        protected override MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();

        public override bool FinishedLaunching(UIApplication application, NSDictionary launchOptions)
        {
            this.SetLocale();
            return base.FinishedLaunching(application, launchOptions);
        }

        private void SetLocale()
        {
            CultureInfo ci = new CultureInfo("el-GR");

            Thread.CurrentThread.CurrentCulture = ci;
            Thread.CurrentThread.CurrentUICulture = ci;
        }
    }
}
