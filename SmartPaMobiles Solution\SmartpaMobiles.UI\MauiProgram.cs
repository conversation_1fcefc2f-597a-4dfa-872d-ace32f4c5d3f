﻿using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmartpaMobiles.UI.Auth;
using SmartpaMobiles.UI.Pages;
using SmartpaMobiles.UI.WebApiClient;
using System.Reflection;
using CommunityToolkit.Maui;
using Syncfusion.Maui.Core.Hosting;
using Syncfusion.Maui.Scheduler;
using System.Globalization;
using AutoMapper;
using SmartpaMobiles.ApiShared.Request;
using Syncfusion.Maui.Toolkit.Hosting;
using SmartpaMobiles.Data.Models;
using System.Resources;
using Microsoft.Extensions.DependencyInjection;
using SmartpaMobiles.UI.Profiles;

namespace SmartpaMobiles.UI
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .UseMauiCommunityToolkit()
                .ConfigureSyncfusionToolkit()        // Initialize the Syncfusion .NET MAUI Toolkit by adding the below line of code
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                });
            builder.ConfigureSyncfusionCore();
            // Initialize the Syncfusion .NET MAUI Toolkit by adding the below line of code


            CultureInfo.CurrentUICulture = new CultureInfo("el-GR");
            //CultureInfo.CurrentCulture = new CultureInfo("el-GR");
            
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Ngo9BigBOggjHTQxAR8/V1JFaF5cXGRCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWXZfeXRSQmhdWEZ1V0RWYEg=");
            //SfSchedulerResources.ResourceManager = new ResourceManager(ResXPath, Application.Current.GetType().Assembly);

#if DEBUG
            builder.Logging.AddDebug();
#endif

            #region Setup configuration
            //Το αρχείο appsettings.<Environment>.json από το FluentBlue.UI.Devices ανάλογα με το Environment ή το Debug/Release. 
#if DEBUG
            string appsettingsEnvironmentFile = "SmartpaMobiles.UI.appsettings.Development.json";
#elif RELEASE
            string appsettingsEnvironmentFile = "SmartpaMobiles.UI.appsettings.Production.json";
#endif
            var a = Assembly.GetExecutingAssembly();
            using var commonAppsettingsStream = a.GetManifestResourceStream("SmartpaMobiles.UI.appsettings.json");  //Διαβάζουμε τα appsettings.json από το SmartpaMobiles.UI με αυτό το τρόπο γιατί στο wwwroot δε δουλεύει.
            using var environmentAppsettingsStream = a.GetManifestResourceStream(appsettingsEnvironmentFile);  //Διαβάζουμε τα appsettings.json ανάλογα με το Environment ή το Debug/Release.
            var config = new ConfigurationBuilder().AddJsonStream(commonAppsettingsStream!).AddJsonStream(environmentAppsettingsStream!).Build();
            builder.Configuration.AddConfiguration(config);
            #endregion

            //AutoMapper
            builder.Services.AddAutoMapper(cfg=>cfg.AddProfile<AppointmentMappingProfile>());

            builder.Services.AddLogging();
            //builder.Services.AddLocalization();
            builder.Services.AddAuthorizationCore();

            #region Depedency Injection
            builder.Services.AddSingleton<HttpClient>(sp =>
            {
                IConfiguration configuration = sp.GetRequiredService<IConfiguration>();
                string? baseUrl = configuration.GetValue<string>("WebApi:BaseUrl");

#if DEBUG
                //Αν είμαστε σε debug και πραγματική συσκευή τότε χρησιμοποιούμε άλλο localhost:port για το WebApi. Θέλει στον browser Edge στη σελιδα edge://inspect/#devices να 
                if (DeviceInfo.Platform == DevicePlatform.Android && DeviceInfo.DeviceType == DeviceType.Physical)
                {
                    baseUrl = configuration.GetValue<string>("WebApi:BaseUrlPsysicalDevice");
                }
                else if (DeviceInfo.Platform == DevicePlatform.Android && DeviceInfo.DeviceType == DeviceType.Virtual)
                {
                    baseUrl = configuration.GetValue<string>("WebApi:BaseUrlVirtualDevice");
                }
#endif

                HttpClient httpClient = new HttpClient { BaseAddress = new Uri(baseUrl!), Timeout = TimeSpan.Parse(configuration.GetValue<string>("WebApi:Timeout")!) };
                //httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AuthenticatedUserData.)
                return httpClient;
            });

            builder.Services.AddScoped<AuthenticationWebApiClient>();  //Για να χρησιμοποιηθεί από το JwtAuthenticationStateProvider
            builder.Services.AddScoped<UsersWebApiClient>();
            builder.Services.AddScoped<ClientsWebApiClient>();
            builder.Services.AddScoped<InsuranceAgenciesWebApiClient>();
            builder.Services.AddScoped<AppointmentsWebApiClient>();
            builder.Services.AddScoped<LogWebApiClient>();

            //Για το JWT
            builder.Services.AddScoped<JwtAuthenticationStateProvider>();
            builder.Services.AddScoped<AuthenticationStateProvider, JwtAuthenticationStateProvider>(
                provider => provider.GetRequiredService<JwtAuthenticationStateProvider>()  //προσθέτουμε αυτή τη γραμμή ώστε σαν object για το JwtAuthenticationStateProvider να χρησιμοποιήσει αυτό που έφτιαξε στην παραπάνω γραμμή.
            );
            builder.Services.AddScoped<ILoginService, JwtAuthenticationStateProvider>(
                provider => provider.GetRequiredService<JwtAuthenticationStateProvider>()  //προσθέτουμε αυτή τη γραμμή ώστε σαν object για το JwtAuthenticationStateProvider να χρησιμοποιήσει αυτό που έφτιαξε παραπάνω.
            );
            builder.Services.AddSingleton<MainPage>();
            builder.Services.AddSingleton<LoginPage>();
            builder.Services.AddSingleton<ClientPage>();
            builder.Services.AddSingleton<ClientsPage>();
            builder.Services.AddSingleton<CalendarPage>();
            builder.Services.AddSingleton<AppointmentPage>();

            #endregion

            return builder.Build();
        }
    }
}
