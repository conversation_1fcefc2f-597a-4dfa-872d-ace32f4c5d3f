using AutoMapper;
using SmartpaMobiles.Data.Models;
using Syncfusion.Maui.Scheduler;

namespace SmartpaMobiles.UI.Profiles
{
    public class AppointmentMappingProfile : Profile
    {
        public AppointmentMappingProfile()
        {
            CreateMap<Appointment, SchedulerAppointment>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.AppointmentId))
                .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => src.StartDate))
                .ForMember(dest => dest.EndTime, opt => opt.MapFrom(src => src.EndDate))
                .ForMember(dest => dest.Subject, opt => opt.MapFrom(src => src.Summary))
                .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.IsAllDay, opt => opt.MapFrom(src => src.AllDay));
                //.ForMember(dest => dest.Background, opt => opt.MapFrom(src => new SolidColorBrush(Color.FromArgb(Appointment.LabelColors.Where(color => color.Id == src.Label!).First().Color.ToArgb().ToString()))));
        }
    }
}