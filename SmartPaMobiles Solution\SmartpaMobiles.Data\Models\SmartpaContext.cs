﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Options;

namespace SmartpaMobiles.Data.Models;

public partial class SmartpaContext : DbContext
{
    private string connectionString = "";

    public SmartpaContext(DbContextOptions<SmartpaContext> options)
        : base(options)
    {
        ChangeTracker.LazyLoadingEnabled = false;
    }

    public SmartpaContext(string connectionString) : base()
    {
        //base.OnConfiguring(new DbContextOptionsBuilder().UseSqlServer(connectionString));
        this.connectionString = connectionString;

        ChangeTracker.LazyLoadingEnabled = false;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        //#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
        //=> optionsBuilder.UseSqlServer("Server=MAIN\\SQLEXPRESS;Database=Omicron2;Trusted_Connection=True;Encrypt=False");

        //Αν δεν έχει οριστεί το connectionstring μέσω του constructor τότε το ορίζουμε μέσω των configuration files
        if (this.connectionString == "")
        {
            optionsBuilder.UseSqlServer("Name=SmartpaMobilesConnectionString");
        }
        else
        {
            optionsBuilder.UseSqlServer(connectionString);
        }

        optionsBuilder.ConfigureWarnings(warnings => warnings.Ignore(CoreEventId.NavigationBaseIncludeIgnored));  //Εισάγουμε αυτή την παράμετρο ώστε να επιτρέπονται multilevel include στα entity framework queries.
    }

    public virtual DbSet<Appointment> Appointments { get; set; }

    public virtual DbSet<AppointmentService> AppointmentServices { get; set; }

    public virtual DbSet<Cexception> Cexceptions { get; set; }

    public virtual DbSet<Client> Clients { get; set; }

    public virtual DbSet<ClientFile> ClientFiles { get; set; }

    public virtual DbSet<DoctorSpecialty> DoctorSpecialties { get; set; }

    public virtual DbSet<Expense> Expenses { get; set; }

    public virtual DbSet<InsuranceAgency> InsuranceAgencies { get; set; }

    public virtual DbSet<Invoice> Invoices { get; set; }

    public virtual DbSet<InvoiceType> InvoiceTypes { get; set; }

    public virtual DbSet<Officer> Officers { get; set; }

    public virtual DbSet<OfficerSetting> OfficerSettings { get; set; }

    public virtual DbSet<Participation> Participations { get; set; }

    public virtual DbSet<PaymentType> PaymentTypes { get; set; }

    public virtual DbSet<RestrictedOfficer> RestrictedOfficers { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<Setting> Settings { get; set; }

    public virtual DbSet<User> Users { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Appointment>(entity =>
        {
            entity.HasIndex(e => e.AllDay, "AllDayIndex").HasFillFactor(80);

            entity.HasIndex(e => e.CreateDate, "CreateDateIndex").HasFillFactor(80);

            entity.HasIndex(e => e.EndDate, "EndDateIndex").HasFillFactor(80);

            entity.HasIndex(e => e.OfficerId, "IX_FK_OfficerAppointment").HasFillFactor(80);

            entity.HasIndex(e => e.StartDate, "StartDateIndex").HasFillFactor(80);

            entity.Property(e => e.AppointmentType)
                .HasMaxLength(20)
                .HasDefaultValue("Appointment")
                .HasComment("Πιθανές τιμές: Appointment=κανονικό ραντεβού (default), OfficeClosed=Ιατρείο κλειστό,");
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.EndDate).HasColumnType("datetime");
            entity.Property(e => e.Location).HasMaxLength(50);
            entity.Property(e => e.RemoteMeetingUrl)
                .HasMaxLength(500)
                .HasDefaultValue("");
            entity.Property(e => e.ResourceId).HasColumnName("ResourceID");
            entity.Property(e => e.ResourceIds).HasColumnName("ResourceIDs");
            entity.Property(e => e.StartDate).HasColumnType("datetime");
            entity.Property(e => e.Subject).HasMaxLength(510);

            entity.HasOne(d => d.CreatedByUser).WithMany(p => p.Appointments)
                .HasForeignKey(d => d.CreatedByUserId)
                .OnDelete(DeleteBehavior.SetNull)
                .HasConstraintName("FK_Appointments_Users");

            entity.HasOne(d => d.Officer).WithMany(p => p.Appointments)
                .HasForeignKey(d => d.OfficerId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OfficerAppointment");
        });

        modelBuilder.Entity<AppointmentService>(entity =>
        {
            entity.Property(e => e.AppointmentDuration).HasDefaultValue(15);
            entity.Property(e => e.ServiceName)
                .HasMaxLength(50)
                .HasDefaultValue("");

            entity.HasOne(d => d.Officer).WithMany(p => p.AppointmentServices)
                .HasForeignKey(d => d.OfficerId)
                .HasConstraintName("FK_AppointmentServices_Officers");
        });

        modelBuilder.Entity<Cexception>(entity =>
        {
            entity.HasKey(e => e.ExceptionId);

            entity.ToTable("CExceptions");

            entity.Property(e => e.ExceptionId).HasColumnName("ExceptionID");
            entity.Property(e => e.ExceptionDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<Client>(entity =>
        {
            entity.HasIndex(e => e.OfficerId, "IX_FK_ClientOfficer").HasFillFactor(80);

            entity.HasIndex(e => e.InsuranceAgencyId, "IX_FK_Client_InsuranceAgency").HasFillFactor(80);

            entity.Property(e => e.Address).HasMaxLength(250);
            entity.Property(e => e.Afm)
                .HasMaxLength(20)
                .HasColumnName("AFM");
            entity.Property(e => e.Ama)
                .HasMaxLength(20)
                .HasColumnName("AMA");
            entity.Property(e => e.Amka)
                .HasMaxLength(20)
                .HasColumnName("AMKA");
            entity.Property(e => e.BirthDate).HasColumnType("datetime");
            entity.Property(e => e.City).HasMaxLength(250);
            entity.Property(e => e.Doy)
                .HasMaxLength(50)
                .HasColumnName("DOY");
            entity.Property(e => e.Email).HasMaxLength(50);
            entity.Property(e => e.Fax).HasMaxLength(20);
            entity.Property(e => e.FirstLastName).HasMaxLength(100);
            entity.Property(e => e.MiddleName).HasMaxLength(50);
            entity.Property(e => e.Mobile1).HasMaxLength(20);
            entity.Property(e => e.Occupation).HasMaxLength(250);
            entity.Property(e => e.Password)
                .HasMaxLength(20)
                .HasDefaultValue("");
            entity.Property(e => e.Phone1).HasMaxLength(20);
            entity.Property(e => e.PoliceIdentity).HasMaxLength(10);
            entity.Property(e => e.PostalCode).HasMaxLength(6);
            entity.Property(e => e.Region).HasMaxLength(250);
            entity.Property(e => e.RowVersion)
                .IsRowVersion()
                .IsConcurrencyToken();

            entity.HasOne(d => d.InsuranceAgency).WithMany(p => p.Clients)
                .HasForeignKey(d => d.InsuranceAgencyId)
                .HasConstraintName("FK_Client_InsuranceAgency");

            entity.HasOne(d => d.Officer).WithMany(p => p.Clients)
                .HasForeignKey(d => d.OfficerId)
                .HasConstraintName("FK_ClientOfficer");
        });

        modelBuilder.Entity<ClientFile>(entity =>
        {
            entity.HasIndex(e => e.ClientId, "IX_FK_ClientFile_Client");

            entity.Property(e => e.Description).HasMaxLength(250);
            entity.Property(e => e.RowVersion)
                .IsRowVersion()
                .IsConcurrencyToken();

            entity.HasOne(d => d.Client).WithMany(p => p.ClientFiles)
                .HasForeignKey(d => d.ClientId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_ClientFile_Client");
        });

        modelBuilder.Entity<DoctorSpecialty>(entity =>
        {
            entity.Property(e => e.RowVersion)
                .IsRowVersion()
                .IsConcurrencyToken();
        });

        modelBuilder.Entity<Expense>(entity =>
        {
            entity.HasIndex(e => e.OfficerId, "IX_FK_OfficerExpense");

            entity.Property(e => e.Afm)
                .HasMaxLength(50)
                .HasColumnName("AFM");
            entity.Property(e => e.Amount).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.BrandName).HasMaxLength(200);
            entity.Property(e => e.DocumentNo).HasMaxLength(50);
            entity.Property(e => e.ExpenseCategory).HasMaxLength(100);
            entity.Property(e => e.ExpenseDate).HasColumnType("datetime");
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.VatAmount).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.VatPercentage).HasColumnType("decimal(18, 2)");

            entity.HasOne(d => d.Officer).WithMany(p => p.Expenses)
                .HasForeignKey(d => d.OfficerId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OfficerExpense");
        });

        modelBuilder.Entity<InsuranceAgency>(entity =>
        {
            entity.Property(e => e.Name).HasMaxLength(500);
        });

        modelBuilder.Entity<Invoice>(entity =>
        {
            entity.HasIndex(e => e.ClientId, "IX_FK_Invoice_Client");

            entity.HasIndex(e => e.OfficerId, "IX_FK_Invoice_Officer");

            entity.HasIndex(e => e.PaymentTypeId, "IX_FK_Invoice_PaymentType");

            entity.HasIndex(e => e.InvoiceTypeId, "IX_FK_Invoice_ReceiptType1");

            entity.Property(e => e.Amount).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.InvoiceDate).HasColumnType("datetime");
            entity.Property(e => e.InvoiceTypeId).HasColumnName("InvoiceTypeID");
            entity.Property(e => e.PaymentTypeId).HasColumnName("PaymentTypeID");
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.VatAmount).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.VatPercentage).HasColumnType("decimal(18, 2)");

            entity.HasOne(d => d.Client).WithMany(p => p.Invoices)
                .HasForeignKey(d => d.ClientId)
                .HasConstraintName("FK_Invoice_Client");

            entity.HasOne(d => d.InvoiceType).WithMany(p => p.Invoices)
                .HasForeignKey(d => d.InvoiceTypeId)
                .HasConstraintName("FK_Invoice_ReceiptType1");

            entity.HasOne(d => d.Officer).WithMany(p => p.Invoices)
                .HasForeignKey(d => d.OfficerId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Invoice_Officer");

            entity.HasOne(d => d.PaymentType).WithMany(p => p.Invoices)
                .HasForeignKey(d => d.PaymentTypeId)
                .HasConstraintName("FK_Invoice_PaymentType");
        });

        modelBuilder.Entity<InvoiceType>(entity =>
        {
            entity.Property(e => e.InvoiceTypeId).HasColumnName("InvoiceTypeID");
            entity.Property(e => e.ShortTypeName).HasMaxLength(20);
            entity.Property(e => e.TypeName).HasMaxLength(50);
        });

        modelBuilder.Entity<Officer>(entity =>
        {
            entity.HasIndex(e => e.DoctorSpecialtyId, "IX_FK_Officer_DoctorSpecialty").HasFillFactor(80);

            entity.Property(e => e.OfficerId).ValueGeneratedNever();
            entity.Property(e => e.Address).HasMaxLength(250);
            entity.Property(e => e.Afm)
                .HasMaxLength(10)
                .HasColumnName("AFM");
            entity.Property(e => e.Announcements).HasDefaultValue("");
            entity.Property(e => e.City).HasMaxLength(250);
            entity.Property(e => e.Doy)
                .HasMaxLength(50)
                .HasColumnName("DOY");
            entity.Property(e => e.Ibans)
                .HasDefaultValue("")
                .HasColumnName("IBANS");
            entity.Property(e => e.OfficerNotes).HasDefaultValue("");
            entity.Property(e => e.PostalCode).HasMaxLength(6);
            entity.Property(e => e.Region).HasMaxLength(250);
            entity.Property(e => e.Resume).HasDefaultValue("");
            entity.Property(e => e.RowVersion)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.SecretaryNotes).HasDefaultValue("");
            entity.Property(e => e.ShowInSmartDoctors).HasDefaultValue(true);

            entity.HasOne(d => d.DoctorSpecialty).WithMany(p => p.Officers)
                .HasForeignKey(d => d.DoctorSpecialtyId)
                .HasConstraintName("FK_Officer_DoctorSpecialty");

            entity.HasOne(d => d.OfficerNavigation).WithOne(p => p.Officer)
                .HasForeignKey<Officer>(d => d.OfficerId)
                .HasConstraintName("FK_Officer_User");
        });

        modelBuilder.Entity<OfficerSetting>(entity =>
        {
            entity.Property(e => e.OfficerSettingId).ValueGeneratedNever();
            entity.Property(e => e.AppointmentNotificationEmailRemarks).HasDefaultValue("");
            entity.Property(e => e.AppointmentsEnabled).HasDefaultValue(true);
            entity.Property(e => e.DefaultSchedulerView).HasMaxLength(20);
            entity.Property(e => e.DefaultVatPercentage).HasColumnType("decimal(18, 0)");
            entity.Property(e => e.InvoiceAddress).HasMaxLength(250);
            entity.Property(e => e.InvoiceBrandName).HasMaxLength(250);
            entity.Property(e => e.InvoiceOccupation).HasMaxLength(250);
            entity.Property(e => e.TimeScale)
                .HasMaxLength(50)
                .HasDefaultValue("");
            entity.Property(e => e.WorkingHourBackColor1)
                .HasMaxLength(20)
                .HasDefaultValue("");
            entity.Property(e => e.WorkingHourBackColor2)
                .HasMaxLength(20)
                .HasDefaultValue("");
            entity.Property(e => e.WorkingHourBackColor3)
                .HasMaxLength(20)
                .HasDefaultValue("");
            entity.Property(e => e.WorkingHourBackColor4)
                .HasMaxLength(20)
                .HasDefaultValue("");

            entity.HasOne(d => d.OfficerSettingNavigation).WithOne(p => p.OfficerSetting)
                .HasForeignKey<OfficerSetting>(d => d.OfficerSettingId)
                .HasConstraintName("FK_OfficerOfficerSetting");
        });

        modelBuilder.Entity<Participation>(entity =>
        {
            entity.HasIndex(e => e.ClientId, "IX_FK_ClientParticipation").HasFillFactor(80);

            entity.HasIndex(e => e.AppointmentId, "IX_FK_Participation_Appointment").HasFillFactor(80);

            entity.HasOne(d => d.Appointment).WithMany(p => p.Participations)
                .HasForeignKey(d => d.AppointmentId)
                .HasConstraintName("FK_Participation_Appointment");

            entity.HasOne(d => d.Client).WithMany(p => p.Participations)
                .HasForeignKey(d => d.ClientId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ClientParticipation");
        });

        modelBuilder.Entity<PaymentType>(entity =>
        {
            entity.Property(e => e.PaymentTypeId).HasColumnName("PaymentTypeID");
            entity.Property(e => e.TypeName).HasMaxLength(50);
        });

        modelBuilder.Entity<RestrictedOfficer>(entity =>
        {
            entity.HasOne(d => d.Officer).WithMany(p => p.RestrictedOfficers)
                .HasForeignKey(d => d.OfficerId)
                .HasConstraintName("FK_RestrictedOfficers_Officers");

            entity.HasOne(d => d.User).WithMany(p => p.RestrictedOfficers)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RestrictedOfficers_Users");
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.Property(e => e.RoleName).HasMaxLength(15);
        });

        modelBuilder.Entity<Setting>(entity =>
        {
            entity.Property(e => e.DefaultVat).HasColumnType("decimal(18, 0)");
            entity.Property(e => e.SecretariesEnvNonWorkingHourBackColor)
                .HasMaxLength(50)
                .HasDefaultValue("#eee");
            entity.Property(e => e.SecretariesEnvWorkingHourBackColor)
                .HasMaxLength(50)
                .HasDefaultValue("");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasIndex(e => e.RoleId, "IX_FK_User_Role");

            entity.HasIndex(e => e.Username, "IX_UniqueUsername").IsUnique();

            entity.Property(e => e.CanDeleteAppointment).HasDefaultValue(true);
            entity.Property(e => e.CanEditAppointment).HasDefaultValue(true);
            entity.Property(e => e.CanEditAppointmentSmsSending).HasDefaultValue(true);
            entity.Property(e => e.Email).HasMaxLength(50);
            entity.Property(e => e.Fax).HasMaxLength(20);
            entity.Property(e => e.FirstLastName).HasMaxLength(100);
            entity.Property(e => e.MiddleName).HasMaxLength(50);
            entity.Property(e => e.Mobile1).HasMaxLength(20);
            entity.Property(e => e.Password).HasMaxLength(20);
            entity.Property(e => e.Phone1).HasMaxLength(20);
            entity.Property(e => e.RowVersion)
                .IsRowVersion()
                .IsConcurrencyToken();
            entity.Property(e => e.Username).HasMaxLength(20);

            entity.HasOne(d => d.Role).WithMany(p => p.Users)
                .HasForeignKey(d => d.RoleId)
                .HasConstraintName("FK_User_Role");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}