﻿using SmartpaMobiles.Shared;
using SmartpaMobiles.ApiShared.Response;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;

namespace SmartpaMobiles.UI.WebApiClient
{
    public class UsersWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<UsersWebApiClient> logger;

        public UsersWebApiClient(HttpClient httpClient, ILogger<UsersWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        //public async Task<List<SmartpaMobiles.Data.Models..UserLI>> GetUsersLI(Guid tenantId)
        //{
        //    try
        //    {
        //        string requestUri = httpClient.BaseAddress + apiVersion + "/" + "Users/li";

        //        //Εκτελούμε το request.
        //        HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);   

        //        string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
        //        ApiResponse<List<Data.Model.DTOs.UserLI>> response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Model.DTOs.UserLI>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

        //        if (response.ResultCode == ApiResponseResultCode.Ok)
        //        {
        //            return response.ResponseContent!;
        //        }
        //        else if (response.ResultCode == ApiResponseResultCode.Exception)
        //        {
        //            throw new ApplicationException(response.ExceptionMessage ?? "");
        //        }

        //        return new List<Data.Model.DTOs.UserLI>();
        //    }
        //    catch (Exception ex)
        //    {
        //        this.logger.LogError(ex, "Error in GetUsersLI({@tenantId})", tenantId);
        //        throw;
        //    }
        //}

        //public async Task<LoginResponse> Login(string username, string password)
        //{
        //    string requestUri = this.httpClient.BaseAddress + @"Users/Login";

        //    requestUri += "?Username=" + username + "&Password=" + password;

        //    string responseString = await this.httpClient.GetStringAsync(requestUri);
        //    ApiResponse<LoginResponse>? response = JsonConvert.DeserializeObject<ApiResponse<LoginResponse>>(responseString);
        //    if (response!.ResultCode == ApiResponseResultCode.Ok)
        //    {
        //        return response.ResponseContent;
        //    }
        //    else if (response.ResultCode == ApiResponseResultCode.Exception)
        //    {
        //        throw new ApplicationException(response.ExceptionMessage ?? "");
        //    }

        //    return null;
        //}

        //public async Task<UserToken> RenewToken()
        //{
        //    string requestUri = this.httpClient.BaseAddress + "RenewToken" ;  

        //    string responseString = await this.httpClient.GetStringAsync(requestUri);
        //    ApiResponse<UserToken>? response = JsonConvert.DeserializeObject<ApiResponse<UserToken>>(responseString);
        //    if (response!.ResultCode == ApiResponseResultCode.Ok)
        //    {
        //        return response.ResponseContent;
        //    }
        //    else if (response.ResultCode == ApiResponseResultCode.Exception)
        //    {
        //        if (response.ExceptionMessageForUser != null)
        //        {
        //            throw new ApplicationException(response.ExceptionMessageForUser ?? "");
        //        }
        //        else
        //        {
        //            throw new Exception(response.ExceptionMessage ?? "");
        //        }
        //    }

        //    return null;

        //}
    }
}
