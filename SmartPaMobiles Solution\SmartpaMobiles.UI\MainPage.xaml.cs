﻿using Microsoft.AspNetCore.Components.Authorization;
using SmartpaMobiles.UI.Pages;

namespace SmartpaMobiles.UI
{
    public partial class MainPage : ContentPage
    {
        int count = 0;
        private AuthenticationStateProvider authenticationStateProvider;

        public MainPage(AuthenticationStateProvider authenticationStateProvider)
        {
            InitializeComponent();

            this.authenticationStateProvider = authenticationStateProvider;
        }

        protected override async void OnNavigatedTo(NavigatedToEventArgs args)
        {
            base.OnNavigatedTo(args);
            AuthenticationState authState;
            authState = await this.authenticationStateProvider.GetAuthenticationStateAsync();

            //Αν ο user δεν είναι authenticated
            if (authState.User.Identity.IsAuthenticated == false)
            {
                await Shell.Current.GoToAsync("//"+nameof(LoginPage));
            }
            else
            {
                await Shell.Current.GoToAsync("//"+nameof(CalendarPage));
            }
        }

       
    }

}
