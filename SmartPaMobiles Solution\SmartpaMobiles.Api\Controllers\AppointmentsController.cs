﻿
using SmartpaMobiles.Business;
using SmartpaMobiles.Shared.Authorization;
using SmartpaMobiles.ApiShared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using System.Text;
using Asp.Versioning;
using AutoMapper;
using SmartpaMobiles.Shared;
using SmartpaMobiles.Business.Request;
using SmartpaMobiles.ApiShared.Request;
using SmartpaMobiles.Data.Models;
using System.Xml;


namespace SmartpaMobiles.Api.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class AppointmentsController : ControllerBase
    {
        private IAppointmentsBusiness appointmentsBusiness;
        private ILogger<AppointmentsController> logger;
        private IMapper mapper;
        private IWebApiCallerInfo webApiCallerInfo;

        public AppointmentsController(IAppointmentsBusiness appointmentBusiness, ILogger<AppointmentsController> logger, IMapper mapper, IWebApiCallerInfo webApiCallerInfo)
        {
            this.appointmentsBusiness = appointmentBusiness;
            this.logger = logger;
            this.mapper = mapper;
            this.webApiCallerInfo = webApiCallerInfo;
        }


        /// <summary>
        /// Retrieves transforms in paged mode.
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ApiResponse<List<SmartpaMobiles.Data.Models.Appointment>>> GetAppointments([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] int officerId)
        {
            try
            {
                startDate = startDate.Date;
                endDate = new DateTime(DateOnly.FromDateTime(endDate), TimeOnly.MaxValue);
                //Validation

                //Query
                List<SmartpaMobiles.Data.Models.Appointment> data = await this.appointmentsBusiness.GetAppointmentsOfOfficer(startDate, endDate, officerId);

                //Response
                return new ApiResponse<List<SmartpaMobiles.Data.Models.Appointment>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "StartDate=" + startDate + " EndDate=" + endDate + " OfficerId=" + officerId);
                return new ApiResponse<List<SmartpaMobiles.Data.Models.Appointment>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }


        [HttpGet]
        [Route("{appointmentId}")]
        public async Task<ApiResponse<Appointment?>> GetAppointment(int officerId, int appointmentId)
        {
            try
            {
                //Query
                Appointment? appointment = await this.appointmentsBusiness.GetAppointment(officerId, appointmentId);

                //Response
                return new ApiResponse<Appointment?>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = appointment };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "AppointmentId=" + appointmentId);
                return new ApiResponse<Appointment?>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }




        [HttpPost]
        [Route("createorupdate")]
        public async Task<ApiResponse<Appointment>> CreateOrUpdateAppointment([FromBody] Appointment appointment)
        {
            try
            {
                //Validation
                if (appointment == null)
                {
                    throw new Exception(SmartpaMobiles.Api.Resources.GlobalResources.InvalidDataMessage);
                }

                if (ModelState.IsValid == false)
                {
                    throw new Exception(ModelState.Values.ToString());
                }

                //Query
                await this.appointmentsBusiness.CreateOrUpdateAppointment(appointment);

                //Response
                Appointment? conctact = await this.appointmentsBusiness.GetAppointment(appointment.OfficerId, appointment.AppointmentId);
                return new ApiResponse<Appointment>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = appointment };
            }
            catch (ApplicationException ex)
            {
                this.logger.LogError(ex, ex.Message, appointment);
                return new ApiResponse<Appointment>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessageForUser = ex.Message };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, appointment);
                return new ApiResponse<Appointment>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpDelete]
        //[Route("{appointmentId}")]
        public ApiResponse DeleteAppointment([FromQuery] string appointmentId)
        {
            try
            {
                //TODO: να μπει έλεγχος ότι η διαγραφή γίνεται από User με το ίδιο TenantId.

                this.appointmentsBusiness.DeleteAppointment(Convert.ToInt32(appointmentId));

                return new ApiResponse() { ResultCode = ApiResponseResultCode.Ok };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "AppointmentId=" + appointmentId);
                return new ApiResponse() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }

        [HttpGet]
        [Route("GetConflictAppointmentsOfOfficer")]
        public async Task<ApiResponse<bool>> GetConflictAppointmentsOfOfficer([FromQuery] int officerId, [FromQuery] int appointmentId, [FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
        {
            try
            {
                //TODO: να μπει έλεγχος ότι η διαγραφή γίνεται από User με το ίδιο TenantId.
                bool conflictsExists = await this.appointmentsBusiness.GetConflictAppointmentsOfOfficer(officerId, appointmentId, startDate, endDate);

                return new ApiResponse<bool>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = conflictsExists};
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message, "AppointmentId=" + appointmentId);
                return new ApiResponse<bool>() { ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
           
        }


        /// <summary>
        /// Returns all the AppointmentServices.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllAppointmentServices")]
        public async Task<ApiResponse<List<SmartpaMobiles.Data.Models.AppointmentService>>> GatAllAppointmentServices([FromQuery] int officerId)
        {
            try
            {
                //Query
                List<SmartpaMobiles.Data.Models.AppointmentService> data = await this.appointmentsBusiness.GetAllAppointmentServices(officerId);

                //Response
                return new ApiResponse<List<SmartpaMobiles.Data.Models.AppointmentService>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message);
                return new ApiResponse<List<SmartpaMobiles.Data.Models.AppointmentService>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }
    }
}
