<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="SmartpaMobiles.UI.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:SmartpaMobiles.UI"
    xmlns:pages="clr-namespace:SmartpaMobiles.UI.Pages"
    Shell.FlyoutBehavior="Flyout"
    Shell.TabBarIsVisible="False" 
    Title="SmartpaMobiles.UI">

    <Shell.FlyoutFooter>
        <StackLayout Padding="20">
            <Label Text="{Binding AppVersion}" 
                   HorizontalOptions="Center" 
                   TextColor="Gray" 
                   FontSize="Small"/>
        </StackLayout>
    </Shell.FlyoutFooter>

    <ShellContent Title="Main" ContentTemplate="{DataTemplate local:MainPage}" Route="MainPage" Shell.FlyoutBehavior="Disabled" FlyoutItemIsVisible="false" />

    <ShellContent Title="Login"  ContentTemplate="{DataTemplate pages:LoginPage}" Route="LoginPage" Shell.FlyoutBehavior="Disabled" FlyoutItemIsVisible="false"/>

   
    <FlyoutItem FlyoutDisplayOptions="AsMultipleItems">
        <!--<ShellContent Title="{x:Static local:Resources.GlobalResources.Home}" ContentTemplate="{DataTemplate pages:HomePage}" Route="HomePage"  />-->

        <ShellContent Title="{x:Static local:Resources.GlobalResources.Clients}" ContentTemplate="{DataTemplate pages:ClientsPage}" Route="ClientsPage" Icon="user.svg" />

        <ShellContent Title="{x:Static local:Resources.GlobalResources.Calendar}" ContentTemplate="{DataTemplate pages:CalendarPage}" Route="CalendarPage" Icon="calendar.svg" />

        <!--<ShellContent x:Name="aboutItem" IsEnabled="False" Title="About Version" Icon="info.png" />-->
    </FlyoutItem>

    <MenuItem Text="{x:Static local:Resources.GlobalResources.SignOut}" Clicked="OnSignOutClicked"  IconImageSource="signout.png" />
    
    <!--<FlyoutItem Title="{x:Static local:Resources.GlobalResources.Home}">
        <ShellContent Title="{x:Static local:Resources.GlobalResources.Home}" ContentTemplate="{DataTemplate pages:HomePage}" Route="HomePage"  />
    </FlyoutItem>
    <FlyoutItem Title="{x:Static local:Resources.GlobalResources.Clients}">
        <ShellContent Title="{x:Static local:Resources.GlobalResources.Clients}" ContentTemplate="{DataTemplate pages:ClientsPage}" Route="ClientsPage" Icon="user.svg" />
    </FlyoutItem>
    <FlyoutItem Title="{x:Static local:Resources.GlobalResources.Calendar}">
        <ShellContent Title="{x:Static local:Resources.GlobalResources.Calendar}" ContentTemplate="{DataTemplate pages:CalendarPage}" Route="CalendarPage" Icon="calendar.svg" />
    </FlyoutItem>-->

    <!--<ShellContent Title="Client"  ContentTemplate="{DataTemplate pages:ClientPage}" Route="ClientPage" Shell.FlyoutBehavior="Disabled" FlyoutItemIsVisible="false"/>-->
    
</Shell>
