﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.ApiShared.Response
{
    public enum ApiResponseResultCode
    {
        Ok,
        Exception
    }

    public class ApiResponse
    {
        public ApiResponseResultCode ResultCode { get; set; }

        //Το μήνυμα σφάλματος που θέλουμε να εμφανίστεί στον χρήστη
        public string? ExceptionMessageForUser { get; set; }
        
        //Το πραγματικό μήνυμα του exception.
        public string? ExceptionMessage { get; set; }
    }

    public class ApiResponse<T> : ApiResponse 
    {
        public T? ResponseContent { get; set; }
    }
}
