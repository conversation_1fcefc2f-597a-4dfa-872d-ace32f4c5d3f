﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.UI
{
    public class ErrorNotifier
    {
        //IDialogService dialogService;

        //public ErrorNotifier(IDialogService dialogService)
        //{
        //    this.dialogService = dialogService;
        //}

        //public async void ShowError(string message, string title)
        //{
        //    await this.dialogService.ShowErrorAsync(message, title);
        //}
    }
}
