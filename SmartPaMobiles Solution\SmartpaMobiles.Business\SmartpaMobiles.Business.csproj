﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="15.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SmartpaMobiles.Data\SmartpaMobiles.Data.csproj" />
    <ProjectReference Include="..\SmartpaMobiles.Shared\SmartpaMobiles.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\GlobalResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>GlobalResources.resx</DependentUpon>
    </Compile>
    <Compile Update="Resources\ValidationResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ValidationResources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\GlobalResources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>GlobalResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\ValidationResources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ValidationResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
