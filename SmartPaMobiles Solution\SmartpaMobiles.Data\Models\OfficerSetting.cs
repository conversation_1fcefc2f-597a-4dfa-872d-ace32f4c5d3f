﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartpaMobiles.Data.Models;

public partial class OfficerSetting : IObjectState
{
    public OfficerSetting()
    {
        this.ObjectState = ObjectState.Unchanged;
    }

    public int OfficerSettingId { get; set; }

    public string DefaultSchedulerView { get; set; } = null!;

    public decimal DefaultVatPercentage { get; set; }

    public string InvoiceBrandName { get; set; } = null!;

    public string InvoiceAddress { get; set; } = null!;

    public string InvoiceOccupation { get; set; } = null!;

    public string TimeScale { get; set; } = null!;

    public TimeOnly? MondayWorkingStartTime1 { get; set; }

    public TimeOnly? MondayWorkingEndTime1 { get; set; }

    public TimeOnly? MondayWorkingStartTime2 { get; set; }

    public TimeOnly? MondayWorkingEndTime2 { get; set; }

    public TimeOnly? MondayWorkingStartTime3 { get; set; }

    public TimeOnly? MondayWorkingEndTime3 { get; set; }

    public TimeOnly? MondayWorkingStartTime4 { get; set; }

    public TimeOnly? MondayWorkingEndTime4 { get; set; }

    public TimeOnly? TuesdayWorkingStartTime1 { get; set; }

    public TimeOnly? TuesdayWorkingEndTime1 { get; set; }

    public TimeOnly? TuesdayWorkingStartTime2 { get; set; }

    public TimeOnly? TuesdayWorkingEndTime2 { get; set; }

    public TimeOnly? TuesdayWorkingStartTime3 { get; set; }

    public TimeOnly? TuesdayWorkingEndTime3 { get; set; }

    public TimeOnly? TuesdayWorkingStartTime4 { get; set; }

    public TimeOnly? TuesdayWorkingEndTime4 { get; set; }

    public TimeOnly? WednesdayWorkingStartTime1 { get; set; }

    public TimeOnly? WednesdayWorkingEndTime1 { get; set; }

    public TimeOnly? WednesdayWorkingStartTime2 { get; set; }

    public TimeOnly? WednesdayWorkingEndTime2 { get; set; }

    public TimeOnly? WednesdayWorkingStartTime3 { get; set; }

    public TimeOnly? WednesdayWorkingEndTime3 { get; set; }

    public TimeOnly? WednesdayWorkingStartTime4 { get; set; }

    public TimeOnly? WednesdayWorkingEndTime4 { get; set; }

    public TimeOnly? ThursdayWorkingStartTime1 { get; set; }

    public TimeOnly? ThursdayWorkingEndTime1 { get; set; }

    public TimeOnly? ThursdayWorkingStartTime2 { get; set; }

    public TimeOnly? ThursdayWorkingEndTime2 { get; set; }

    public TimeOnly? ThursdayWorkingStartTime3 { get; set; }

    public TimeOnly? ThursdayWorkingEndTime3 { get; set; }

    public TimeOnly? ThursdayWorkingStartTime4 { get; set; }

    public TimeOnly? ThursdayWorkingEndTime4 { get; set; }

    public TimeOnly? FridayWorkingStartTime1 { get; set; }

    public TimeOnly? FridayWorkingEndTime1 { get; set; }

    public TimeOnly? FridayWorkingStartTime2 { get; set; }

    public TimeOnly? FridayWorkingEndTime2 { get; set; }

    public TimeOnly? FridayWorkingStartTime3 { get; set; }

    public TimeOnly? FridayWorkingEndTime3 { get; set; }

    public TimeOnly? FridayWorkingStartTime4 { get; set; }

    public TimeOnly? FridayWorkingEndTime4 { get; set; }

    public TimeOnly? SaturdayWorkingStartTime1 { get; set; }

    public TimeOnly? SaturdayWorkingEndTime1 { get; set; }

    public TimeOnly? SaturdayWorkingStartTime2 { get; set; }

    public TimeOnly? SaturdayWorkingEndTime2 { get; set; }

    public TimeOnly? SaturdayWorkingStartTime3 { get; set; }

    public TimeOnly? SaturdayWorkingEndTime3 { get; set; }

    public TimeOnly? SaturdayWorkingStartTime4 { get; set; }

    public TimeOnly? SaturdayWorkingEndTime4 { get; set; }

    public TimeOnly? SundayWorkingStartTime1 { get; set; }

    public TimeOnly? SundayWorkingEndTime1 { get; set; }

    public TimeOnly? SundayWorkingStartTime2 { get; set; }

    public TimeOnly? SundayWorkingEndTime2 { get; set; }

    public TimeOnly? SundayWorkingStartTime3 { get; set; }

    public TimeOnly? SundayWorkingEndTime3 { get; set; }

    public TimeOnly? SundayWorkingStartTime4 { get; set; }

    public TimeOnly? SundayWorkingEndTime4 { get; set; }

    public bool RemoteMeetings { get; set; }

    public string WorkingHourBackColor1 { get; set; } = null!;

    public string WorkingHourBackColor2 { get; set; } = null!;

    public string WorkingHourBackColor3 { get; set; } = null!;

    public string WorkingHourBackColor4 { get; set; } = null!;

    public string AppointmentNotificationEmailRemarks { get; set; } = null!;

    public bool SmsEnabled { get; set; }

    public bool AppointmentsEnabled { get; set; }

    public bool MondayWorkZone1RemoteMeetingsOnly { get; set; }

    public bool MondayWorkZone2RemoteMeetingsOnly { get; set; }

    public bool MondayWorkZone3RemoteMeetingsOnly { get; set; }

    public bool MondayWorkZone4RemoteMeetingsOnly { get; set; }

    public bool TuesdayWorkZone1RemoteMeetingsOnly { get; set; }

    public bool TuesdayWorkZone2RemoteMeetingsOnly { get; set; }

    public bool TuesdayWorkZone3RemoteMeetingsOnly { get; set; }

    public bool TuesdayWorkZone4RemoteMeetingsOnly { get; set; }

    public bool WednesdayWorkZone1RemoteMeetingsOnly { get; set; }

    public bool WednesdayWorkZone2RemoteMeetingsOnly { get; set; }

    public bool WednesdayWorkZone3RemoteMeetingsOnly { get; set; }

    public bool WednesdayWorkZone4RemoteMeetingsOnly { get; set; }

    public bool ThursdayWorkZone1RemoteMeetingsOnly { get; set; }

    public bool ThursdayWorkZone2RemoteMeetingsOnly { get; set; }

    public bool ThursdayWorkZone3RemoteMeetingsOnly { get; set; }

    public bool ThursdayWorkZone4RemoteMeetingsOnly { get; set; }

    public bool FridayWorkZone1RemoteMeetingsOnly { get; set; }

    public bool FridayWorkZone2RemoteMeetingsOnly { get; set; }

    public bool FridayWorkZone3RemoteMeetingsOnly { get; set; }

    public bool FridayWorkZone4RemoteMeetingsOnly { get; set; }

    public bool SaturdayWorkZone1RemoteMeetingsOnly { get; set; }

    public bool SaturdayWorkZone2RemoteMeetingsOnly { get; set; }

    public bool SaturdayWorkZone3RemoteMeetingsOnly { get; set; }

    public bool SaturdayWorkZone4RemoteMeetingsOnly { get; set; }

    public bool SundayWorkZone1RemoteMeetingsOnly { get; set; }

    public bool SundayWorkZone2RemoteMeetingsOnly { get; set; }

    public bool SundayWorkZone3RemoteMeetingsOnly { get; set; }

    public bool SundayWorkZone4RemoteMeetingsOnly { get; set; }

    public Officer OfficerSettingNavigation { get; set; } = null!;

    [NotMapped]
    public ObjectState ObjectState { get; set; }
}