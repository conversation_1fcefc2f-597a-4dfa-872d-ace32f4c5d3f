﻿using CommunityToolkit.Maui.Core.Extensions;
using MvvmHelpers;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Shared;
using SmartpaMobiles.UI.Auth;
using SmartpaMobiles.UI.WebApiClient;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.UI.ViewModels
{
    internal class ClientViewModel : BaseViewModel
    {
        protected ClientsWebApiClient clientsWebApiClient;
        protected Client client;
        protected List<Data.Models.Appointment> originalAppointments;
        protected List<Data.Models.Appointment> appointments;
        protected string filter = "";

        public Client Client
        {
            get
            {
                return this.client;
            }
            set
            {
                this.client = value;
            }
        }

        public ObservableRangeCollection<Data.Models.Appointment> Appointments
        {
            get; set;
        }

        public string Filter
        {
            get
            {
                return filter;
            }
            set
            {
                this.filter = value;
                if (filter.Trim() != "")
                {
                    Appointments.Clear();
                    var temp = this.originalAppointments.Where(x => x.Subject.ToLower().Contains(Filter.ToLower()) || x.Description.Contains(Filter.ToLower())).ToList();
                    Appointments.AddRange(temp);
                }
                else
                {
                    Appointments.Clear();
                    Appointments.AddRange(this.originalAppointments);
                }
            }
        }

        public ClientViewModel(ClientsWebApiClient clientsWebApiClient)
        {
            originalAppointments = new List<Data.Models.Appointment>();
            Appointments = new ObservableRangeCollection<Data.Models.Appointment>();
            this.clientsWebApiClient = clientsWebApiClient;
        }

        public async Task GetClientData(int clientId)
        {
            Client clientResult = await this.clientsWebApiClient.GetClient(clientId);
            this.client = clientResult;
            //this.originalAppointments.Clear();
            //this.originalAppointments = result.Data!;

            //if (filter.Trim() != "")
            //{
            //    Appointments.Clear();
            //    Appointments.AddRange(this.originalAppointments.Where(x => x.Subject.ToLower().Contains(Filter.ToLower()) || x.Description.Contains(Filter.ToLower())));
            //}
            //else
            //{
            //    Appointments.Clear();
            //    Appointments.AddRange(this.originalAppointments);
            //}
        }

        public async Task RefreshAppointments()
        {
            //PagedData<List<Data.DTOs.ClientView>> result = await this.clientsWebApiClient.(AuthenticatedUserData.OfficerId, "", 0, 1000);
            //this.originalAppointments.Clear();
            //this.originalAppointments = result.Data!;

            //if (filter.Trim() != "")
            //{
            //    Appointments.Clear();
            //    Appointments.AddRange(this.originalAppointments.Where(x => x.Subject.ToLower().Contains(Filter.ToLower()) || x.Description.Contains(Filter.ToLower())));
            //}
            //else
            //{
            //    Appointments.Clear();
            //    Appointments.AddRange(this.originalAppointments);
            //}
        }


    }
}
