<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="SmartpaMobiles.UI.Pages.CalendarPage"
             xmlns:scheduler="clr-namespace:Syncfusion.Maui.Scheduler;assembly=Syncfusion.Maui.Scheduler"
             xmlns:project="clr-namespace:SmartpaMobiles.UI"
             Title="{x:Static project:Pages.Resources.CalendarPageResources.Title}">

    <ContentPage.ToolbarItems>
        <ToolbarItem Text="{x:Static project:Resources.GlobalResources.Save}" Order="Primary" IconImageSource="add.png" Clicked="newAppointmentBtn_Clicked">
        </ToolbarItem>
    </ContentPage.ToolbarItems>

    <scheduler:SfScheduler x:Name="scheduler" VerticalOptions="Fill" AllowedViews="Week,WorkWeek,Month,Day,Agenda" Tapped="Scheduler_Tapped"  ViewChanged="SchedulerViewChanged" LongPressed="SchedulerLongPressed" SelectionChanged="SchedulerSelectionChanged" Focused="scheduler_Focused" QueryAppointments="scheduler_QueryAppointments">
        <scheduler:SfScheduler.DaysView>
            <scheduler:SchedulerDaysView NumberOfVisibleDays="1" TimeFormat="HH:mm"/>
        </scheduler:SfScheduler.DaysView>
        <scheduler:SfScheduler.MonthView AppointmentDisplayMode="Indicator"/>
        <scheduler:SfScheduler.AgendaView>
            <scheduler:SchedulerAgendaView AppointmentTimeFormat="HH:mm"/>
        </scheduler:SfScheduler.AgendaView>

        <!--<scheduler:SfScheduler.AppointmentMapping>

            <scheduler:SchedulerAppointmentMapping
            Subject="Subject"
            StartTime="StartDate"
            EndTime="EndDate"
            IsAllDay="AllDay"
            Id="AppointmentId"        
            />

        </scheduler:SfScheduler.AppointmentMapping>-->
    </scheduler:SfScheduler>

</ContentPage>