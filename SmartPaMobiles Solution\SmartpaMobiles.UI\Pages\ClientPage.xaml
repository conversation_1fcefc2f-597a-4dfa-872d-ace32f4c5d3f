<?xml version="1.0" encoding="utf-8" ?>
<ContentPage  xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:project="clr-namespace:SmartpaMobiles.UI"
             xmlns:model="clr-namespace:SmartpaMobiles.Data.Models;assembly=SmartpaMobiles.Data"
             xmlns:android="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.AndroidSpecific;assembly=Microsoft.Maui.Controls"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             xmlns:tabView="clr-namespace:Syncfusion.Maui.Toolkit.TabView;assembly=Syncfusion.Maui.Toolkit"
             xmlns:converters="clr-namespace:SmartpaMobiles.UI.Utilities"
             x:Class="SmartpaMobiles.UI.Pages.ClientPage"
             Title="{x:Static project:Pages.Resources.ClientPageResources.Title}">


    <ContentPage.ToolbarItems>
        <ToolbarItem Text="{x:Static project:Resources.GlobalResources.Save}" Order="Primary" IconImageSource="check.png" Clicked="saveToolbarItem_Clicked">
        </ToolbarItem>
        <ToolbarItem Text="{x:Static project:Resources.GlobalResources.Delete}" Order="Secondary" IconImageSource="delete.png" Clicked="deleteToolbarItem_Clicked">
        </ToolbarItem>
    </ContentPage.ToolbarItems>

    <ContentPage.Resources>
        <ResourceDictionary>
            <toolkit:InvertedBoolConverter x:Key="InvertedBoolConverter" />
            <converters:BooleanToStrikethroughConverter x:Key="booleanToStrikethroughConverter" />
        </ResourceDictionary>

        <Style x:Key="InvalidEntryStyle" TargetType="Entry">
            <Setter Property="TextColor" Value="Red" />
        </Style>
    </ContentPage.Resources>

    <tabView:SfTabView TabBarPlacement="Bottom" TabBarHeight="60">
        <tabView:SfTabItem Header="{x:Static project:Pages.Resources.ClientPageResources.GeneralTabTitle}" ImageSource="form.png">
            <tabView:SfTabItem.Content>
                <ScrollView>
                    <Grid RowDefinitions="*" Padding="5">
                        <Border Style="{StaticResource FlatBorder}" >
                            <VerticalStackLayout>
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.ClientId}" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding ClientId}" IsReadOnly="True"></Entry>
                                
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.FirstLastName}" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding FirstLastName}" MaxLength="100"></Entry>
                                
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.MiddleName}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding MiddleName}"  MaxLength="50"></Entry>
                                
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Amka}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry x:Name="amkaEntry"  Text="{Binding Amka}" MaxLength="20">
                                    <Entry.Behaviors>
                                        <toolkit:RequiredStringValidationBehavior  InvalidStyle="{StaticResource InvalidEntryStyle}" Flags="ValidateOnAttaching" />        
                                    </Entry.Behaviors>
                                </Entry>
                                
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Mobile}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding Mobile1}" MaxLength="20"></Entry>
                                
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Phone}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding Phone1}" MaxLength="20"></Entry>
                                
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.InsuranceAgency}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Picker SelectedItem="{Binding insuranceAgency}" x:Name="insuranceAgencyPicker" />
                                
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.BirthDate}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <DatePicker Date="{Binding BirthDate}" />
                                
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Age}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Label Text="{Binding Age}" />
                                
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Fax}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding Fax}" MaxLength="20"></Entry>
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Email}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding Email}" MaxLength="50"></Entry>
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Occupation}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding Occupation}" MaxLength="250"></Entry>
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Ama}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding Ama}" MaxLength="20"></Entry>
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Afm}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding Afm}" MaxLength="20"></Entry>
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Doy}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding Doy}" MaxLength="50"></Entry>
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.Address}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding Address}" MaxLength="250"></Entry>
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.City}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding City}" MaxLength="250"></Entry>
                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.PostalCode}" HorizontalOptions="Start" StyleClass="LeftTopMarginLabel"></Label>
                                <Entry Text="{Binding PostalCode}" MaxLength="6"></Entry>

                            </VerticalStackLayout>
                        </Border>
                    </Grid>
                </ScrollView>
            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>
        <tabView:SfTabItem Header="{x:Static project:Pages.Resources.ClientPageResources.AppointmentsTabTitle}" ImageSource="appointment.png" >
            <tabView:SfTabItem.Content>
                <Grid RowDefinitions="*" Padding="5">
                    <CollectionView x:Name="clientsCV" SelectionMode="Single" ItemsSource="{Binding Participations}" >
                        <CollectionView.EmptyView>
                            <Label Text="{x:Static project:Resources.GlobalResources.NoData}" HorizontalTextAlignment="Center" />
                        </CollectionView.EmptyView>
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="model:Participation">
                                <Grid Padding="0" Margin="2">
                                    <Border Style="{StaticResource FlatBorder}" >
                                        <VerticalStackLayout >
                                            <HorizontalStackLayout>
                                                <Label Text="{Binding Appointment.StartDate, StringFormat='{0:dd-MM-yyyy}'}" IsEnabled="{Binding Appointment.Canceled, Converter={StaticResource InvertedBoolConverter}}" FontAttributes="Bold" TextDecorations="{Binding Appointment.Canceled, Converter={StaticResource booleanToStrikethroughConverter}}" />
                                                <Label Text="{x:Static project:Pages.Resources.ClientPageResources.AllDayLowerWithSpace}" IsVisible="{Binding Appointment.AllDay}" IsEnabled="{Binding Appointment.Canceled, Converter={StaticResource InvertedBoolConverter}}" TextDecorations="{Binding Appointment.Canceled, Converter={StaticResource booleanToStrikethroughConverter}}"></Label>
                                                <Label Text=" "></Label>
                                                <Label Text="{Binding Appointment.StartDate, StringFormat='{0:HH:mm}'}" IsVisible="{Binding Appointment.AllDay, Converter={StaticResource InvertedBoolConverter}}" IsEnabled="{Binding Appointment.Canceled, Converter={StaticResource InvertedBoolConverter}}" FontAttributes="Bold" TextDecorations="{Binding Appointment.Canceled, Converter={StaticResource booleanToStrikethroughConverter}}" />
                                                <Label Text=" - " IsVisible="{Binding Appointment.AllDay, Converter={StaticResource InvertedBoolConverter}}" IsEnabled="{Binding Appointment.Canceled, Converter={StaticResource InvertedBoolConverter}}" TextDecorations="{Binding Appointment.Canceled, Converter={StaticResource booleanToStrikethroughConverter}}"></Label>
                                                <Label Text="{Binding Appointment.EndDate, StringFormat='{0:HH:mm}'}" IsVisible="{Binding Appointment.AllDay, Converter={StaticResource InvertedBoolConverter}}" IsEnabled="{Binding Appointment.Canceled, Converter={StaticResource InvertedBoolConverter}}" FontAttributes="Bold" TextDecorations="{Binding Appointment.Canceled, Converter={StaticResource booleanToStrikethroughConverter}}" />

                                            </HorizontalStackLayout>
                                            <Label Text="{Binding Appointment.Summary}" IsEnabled="{Binding Appointment.Canceled, Converter={StaticResource InvertedBoolConverter}}" TextDecorations="{Binding Appointment.Canceled, Converter={StaticResource booleanToStrikethroughConverter}}"/>
                                        </VerticalStackLayout>

                                        <Border.GestureRecognizers>
                                            <TapGestureRecognizer Tapped="AppointmentsTapGesture_Tapped"  CommandParameter="{Binding AppointmentId}">
                                            </TapGestureRecognizer>
                                        </Border.GestureRecognizers>
                                    </Border>
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </Grid>
            </tabView:SfTabItem.Content>
        </tabView:SfTabItem>
    </tabView:SfTabView>
</ContentPage>