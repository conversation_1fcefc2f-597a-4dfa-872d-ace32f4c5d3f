﻿
using SmartpaMobiles.Business;
using SmartpaMobiles.Shared.Authorization;
using SmartpaMobiles.ApiShared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using System.Text;
using Asp.Versioning;
using AutoMapper;
using SmartpaMobiles.Shared;
using SmartpaMobiles.Business.Request;
using SmartpaMobiles.ApiShared.Request;
using SmartpaMobiles.Data.Models;


namespace SmartpaMobiles.Api.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("v{version:apiVersion}/insurance-agencies")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]  //TODO: να φτιάξω να υποστηρίζει και το Authorize(Policy="user/admin") έτσι ώστε ορισμένα σε WebApi endpoints να έχει πρόσβαση μόνο οι "απλοί" χρήστες, και σε άλλα endpoints μόνο οι admin

    public class InsuranceAgenciesController : ControllerBase
    {
        private IInsuranceAgenciesBusiness insuranceAgenciesBusiness;
        private ILogger<InsuranceAgenciesController> logger;
        private IMapper mapper;
        private IWebApiCallerInfo webApiCallerInfo;

        public InsuranceAgenciesController(IInsuranceAgenciesBusiness insuranceAgenciesBusiness, ILogger<InsuranceAgenciesController> logger, IMapper mapper, IWebApiCallerInfo webApiCallerInfo)
        {
            this.insuranceAgenciesBusiness = insuranceAgenciesBusiness;
            this.logger = logger;
            this.mapper = mapper;
            this.webApiCallerInfo = webApiCallerInfo;
        }


        /// <summary>
        /// Returns all the InsuranceAgencies.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        //[Route("li")]
        public async Task<ApiResponse<List<SmartpaMobiles.Data.Models.InsuranceAgency>>> GatAll()
        {
            try
            {
                //Query
                List<SmartpaMobiles.Data.Models.InsuranceAgency> data = await this.insuranceAgenciesBusiness.GetAll();

                //Response
                return new ApiResponse<List<SmartpaMobiles.Data.Models.InsuranceAgency>>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = data };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, ex.Message);
                return new ApiResponse<List<SmartpaMobiles.Data.Models.InsuranceAgency>>() { ResponseContent = null, ResultCode = ApiResponseResultCode.Exception, ExceptionMessage = ex.Message };
            }
        }
    }
}
