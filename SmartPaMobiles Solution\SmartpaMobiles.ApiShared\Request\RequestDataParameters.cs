﻿using SmartpaMobiles.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.ApiShared.Request
{
    public class RequestDataParameters
    {
        public Int64 OfficerId { get; set; } 
        public string? Filter { get; set; } = string.Empty;
        public UInt16 PageIndex { get; set; } = 0;
        public UInt16 PageSize { get; set; } = 10;
        public Dictionary<string, SortOrder> SortColumns { get; set; } = new Dictionary<string, SortOrder>();
    }
}
