﻿using SmartpaMobiles.Business;
using SmartpaMobiles.Shared.Authorization;
using SmartpaMobiles.ApiShared.Response;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using SmartpaMobiles.Shared;
using SmartpaMobiles.Shared.Cryptography.AES;
using Azure.Core;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.ApiShared.Request;

namespace SmartpaMobiles.Api.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class AuthenticationController : ControllerBase
    {
        private ILogger<AuthenticationController> logger;
        private IUsersBusiness usersBusiness;
        private IConfiguration configuration;

        public AuthenticationController(IUsersBusiness usersBusiness, ILogger<AuthenticationController> logger, IConfiguration configuration)
        {
            this.usersBusiness = usersBusiness;
            this.logger = logger;  //TODO: να δω αν θα χρησιμοποιείσω το ILogger γενικά στην εφαρμογή.
            this.configuration = configuration;
        }

        [HttpPost("Login")]
        public async Task<ApiResponse<LoginResponse>> Login([FromBody] LoginParameters loginParameters)
        {
            //string base64Encoded = loginParameters.Username; // Assuming request contains encrypted Base64 string
            //byte[] encryptedBytes = Convert.FromBase64String(base64Encoded);
            //string decryptedUsername = AesCryptography.Decrypt(Encoding.UTF8.GetString(encryptedBytes));

            //base64Encoded = password; // Assuming request contains encrypted Base64 string
            //encryptedBytes = Convert.FromBase64String(base64Encoded);
            //string decryptedPassword = AesCryptography.Decrypt(Encoding.UTF8.GetString(encryptedBytes));

            string decryptedUsername = AesCryptography.Decrypt(loginParameters.Username!);
            string decryptedPassword = AesCryptography.Decrypt(loginParameters.Password!);

        

            //Query
            Data.Models.User? user = await this.usersBusiness.GetUser(decryptedUsername, decryptedPassword);

            //Αν ο User υπάρχει
            if (user != null)
            {
                string officerId = "";
                if (user.Officer != null)  //Αν ο χρήστης είναι Officer.
                {
                    officerId = user.Officer.OfficerId.ToString();
                }

                UserToken token = BuildToken(user.UserId.ToString(), decryptedUsername, user.FirstLastName, user.Role.RoleName.ToString(), officerId);

                //Response
                LoginResponse loginResponse = new LoginResponse() { LoginResult = LoginResult.Ok, Token = token };
                return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = loginResponse };
            }
            else  //Αν ο User δεν υπάρχει
            {
                //Response
                LoginResponse loginResponse = new LoginResponse() { LoginResult = LoginResult.InvalidUsernamePassword, Token = new UserToken() };
                return new ApiResponse<LoginResponse>() { ResultCode = ApiResponseResultCode.Ok, ResponseContent = loginResponse };
            }
        }

        [HttpGet("RenewToken")]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public ActionResult<UserToken> Renew()
        {
            UserToken userToken = BuildToken("9AD0538F-EAEA-4C4B-8FE7-B69CEDDCABD3", HttpContext.User.Identity.Name, "John", "Admin", "FD335871-9253-4364-9BEB-5D1238F1C4E5");

            return userToken;
        }

        private UserToken BuildToken(string userId, string username, string fullName, string roleName, string officerId)
        {
            var claims = new List<Claim>()
            {
                new Claim("UserId", userId),
                new Claim("Username", username),
                new Claim(ClaimTypes.Name, fullName),
                new Claim(ClaimTypes.Role, roleName)
            };
            if (officerId != "")
            {
                claims.Add(new Claim("OfficerId", officerId));
            }

            //var identityUser = await _userManager.FindByEmailAsync(userinfo.Email);
            //var claimsDB = await _userManager.GetClaimsAsync(identityUser);
            //claims.AddRange(claimsDB);

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["jwt:key"]!));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var expiration = DateTime.UtcNow.AddYears(1);

            JwtSecurityToken token = new JwtSecurityToken(
               issuer: null,
               audience: null,
               claims: claims,
               expires: expiration,
               signingCredentials: creds);

            return new UserToken()
            {
                Token = new JwtSecurityTokenHandler().WriteToken(token),
                Expiration = expiration
            };
        }

    }
}
