﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartpaMobiles.ApiShared.Request
{
    public class RequestEventsParameters
    {
        public Int64 OfficerId { get; set; }  
        public string? Filter { get; set; } = "";
        public long StartDateTicks { get; set; }
        public long EndDateTicks { get; set; }
    }
}
