﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

using Asp.Versioning;
using SmartpaMobiles.Api;

namespace SmartpaMobiles.API.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class LogController : ControllerBase
    {
        private SmartpaMobiles.Data.Models.SmartpaContext dbContext;
        private readonly IConfiguration configuration;
        private Microsoft.Extensions.Hosting.IHostEnvironment? hostEnvironment = null;

        public LogController(SmartpaMobiles.Data.Models.SmartpaContext dbContext, IConfiguration configuration, Microsoft.Extensions.Hosting.IHostEnvironment hostEnv)
        {
            this.dbContext = dbContext;
            this.configuration = configuration;
            this.hostEnvironment = hostEnv;
        }

        [HttpPost("LogException")]
        public async Task LogException([FromBody] Exception ex, [FromQuery] string product)
        {
            try
            {
                new ExceptionHandler(hostEnvironment, product).LogException(ex);
            }
            catch
            {
            }
           
        }

    }
}
