﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SmartpaMobiles.Data.Models;

public partial class Client : IObjectState
{

    public Client()
    {
        this.ObjectState = ObjectState.Unchanged;
    }

    [Key]
    public int ClientId { get; set; }

    public int OfficerId { get; set; }

    public string FirstLastName { get; set; } = string.Empty!;

    public string MiddleName { get; set; } = string.Empty!;

    public string Occupation { get; set; } = string.Empty!;

    public string Email { get; set; } = string.Empty!;

    public string Phone1 { get; set; } = string.Empty!;

    public string Fax { get; set; } = string.Empty!;

    public string Mobile1 { get; set; } = string.Empty!;

    public string Address { get; set; } = string.Empty!;

    public string City { get; set; } = string.Empty!;

    public string Region { get; set; } = string.Empty!;

    public string PostalCode { get; set; } = string.Empty!;

    public DateTime? BirthDate { get; set; }

    public short Sex { get; set; }

    public string PoliceIdentity { get; set; } = string.Empty!;

    public string Ama { get; set; } = string.Empty!;

    public string Amka { get; set; } = string.Empty!;

    public int? InsuranceAgencyId { get; set; }

    public string Notes { get; set; } = string.Empty!;

    public byte[]? Photo { get; set; }

    public byte[] RowVersion { get; set; } = new byte[0];

    public string Afm { get; set; } = string.Empty!;

    public string Doy { get; set; } = string.Empty!;

    public string Password { get; set; } = string.Empty!;

    public ICollection<ClientFile> ClientFiles { get; set; } = new List<ClientFile>();

    public InsuranceAgency? InsuranceAgency { get; set; }

    public ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();

    public Officer? Officer { get; set; } = null!;

    public ICollection<Participation> Participations { get; set; } = new List<Participation>();

    [NotMapped]
    public ObjectState ObjectState { get; set; }

    [NotMapped]
    public string Initials
    {
        get
        {
            try
            {
                string fixedFirstLastName = FirstLastName.Replace("  ", " ").Trim();
                if (fixedFirstLastName.Length > 0)
                {
                    return fixedFirstLastName.Split(' ')[0].Substring(0, 1).ToUpper() + (FirstLastName.Split(' ').Length > 1 ? (FirstLastName.Split(' ')[1].Length > 0 ? FirstLastName.Split(' ')[1][0].ToString().ToUpper() : string.Empty) : "");
                }
                else
                {
                    return "";
                }
            }
            catch(Exception ex)
            {
                return string.Empty;
            }
        }
    }

    [NotMapped]
    public int? Age
    {
        get
        {
            if (BirthDate != null)
            {
                return (DateTime.Today - BirthDate.Value).Days / 365;
            }
            else
            {
                return null;
            }
        }
    }
}