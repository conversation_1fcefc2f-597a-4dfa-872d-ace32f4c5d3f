﻿using SmartpaMobiles.ApiShared;
using SmartpaMobiles.Shared;
using System.Text;
using Newtonsoft.Json;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Data.DTOs;
using SmartpaMobiles.ApiShared.Request;
using SmartpaMobiles.ApiShared.Response;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using SmartpaMobiles.Shared.Authorization;


namespace SmartpaMobiles.UI.WebApiClient
{
    public class AppointmentsWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<AppointmentsWebApiClient> logger;

        public AppointmentsWebApiClient(HttpClient httpAppointment, ILogger<AppointmentsWebApiClient> logger)
        {
            this.httpClient = httpAppointment;
            this.logger = logger;
        }

        public async Task<List<SmartpaMobiles.Data.Models.Appointment>> GetAppointments(DateTime startDate, DateTime endDate, int officerId)
        {
            try
            {
                //startDate = new DateTime(startDate.Ticks, DateTimeKind.Utc);
                //endDate = new DateTime(endDate.Ticks, DateTimeKind.Utc);
                string requestUri = httpClient.BaseAddress + apiVersion + "/" + "appointments" + "?StartDate=" + startDate.ToString("yyyy-MM-dd") + "&EndDate=" + endDate.ToString("yyyy-MM-dd") + "&OfficerId=" + officerId.ToString();

                //RequestDataParameters requestDataParams = new RequestDataParameters();
                ////requestDataParams.TenantId = tenantId;
                //requestDataParams.OfficerId = officerId;
                //requestDataParams.Filter = filter.Trim();
                //requestDataParams.PageIndex = pageIndex;
                //requestDataParams.PageSize = pageSize;
                //requestDataParams.SortColumns = new Dictionary<string, SortOrder>();
                //requestDataParams.SortColumns.Add("FirstLastName", SortOrder.Ascending);
                //requestDataParams.SortColumns.Add("MiddleName", SortOrder.Ascending);
                //string getEntitiesParamsJson = JsonConvert.SerializeObject(requestDataParams);  //Μετατρέπουμε τις παραμέτρους για το διάβασμα entities σε json.

                ////Ετοιμάζουμε το HttpContext με τα json data.
                //HttpContent httpContent = new StringContent(getEntitiesParamsJson, Encoding.UTF8, "application/json");

                //string urlParameters = UrlHelpers.ToQueryString(requestDataParams, "&");
                //requestUri = requestUri + "?" + urlParameters;

                //Εκτελούμε το request.
                //this.httpAppointment.DefaultRequestHeaders.Authorization= new AuthenticationHeaderValue("Bearer", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOiIyY2FhYmI1NC1mZDM4LTRhY2MtOTMyYS1kNWM3Yjk0ZmRkMmYiLCJVc2VybmFtZSI6IjMzIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZSI6IlAgTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IlN1cGVyQWRtaW4iLCJUZW5hbnRJZCI6IjNmYTg1ZjY0LTU3MTctNDU2Mi1iM2ZjLTJjOTYzZjY2YWZhNiIsImV4cCI6MTc0NzA0Mjk2Mn0.ZqUnCMKdLJ37E2PBZJJVTaFXpVBa5skxbHhBY18wTjs");
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET           
                //HttpResponseMessage httpResponse = await this.httpAppointment.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Models.Appointment>> response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Models.Appointment>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    throw new ApplicationException(response.ExceptionMessage ?? "");
                }

                return new List<Data.Models.Appointment>();
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetAppointments({@officerId},{StartDate},{@EndDate})", officerId, startDate, endDate);
                throw;
            }
        }


        public async Task<Appointment?> GetAppointment(int appointmentId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "appointments" + "/" + appointmentId.ToString();  // + "&tenantId=" + tenantId;

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<Appointment?>? response = JsonConvert.DeserializeObject<ApiResponse<Data.Models.Appointment?>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetAppointment({@appointmentId})", appointmentId);
                throw;
            }
        }

        public async Task<Appointment?> CreateOrUpdateAppointment(Appointment appointment)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/" + "appointments/createorupdate";

                //string AppointmentJson = Newtonsoft.Json.JsonConvert.SerializeObject(Appointment, Newtonsoft.Json.Formatting.Indented);  //Μετατρέπουμε το Appointment object σε json.
                string AppointmentJson = System.Text.Json.JsonSerializer.Serialize(appointment);  //Μετατρέπουμε το Appointment object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(AppointmentJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<Appointment>? response = JsonConvert.DeserializeObject<ApiResponse<Appointment>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateAppointment({@tenant})", appointment);
                throw;
            }
        }

        public async Task DeleteAppointment(int appointmentId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "appointments" + "?appointmentId=" + appointmentId.ToString();

                HttpResponseMessage httpResponse = await this.httpClient.DeleteAsync(requestUri);
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse? response = JsonConvert.DeserializeObject<ApiResponse>(responseString);
                if (response != null)
                {
                    if (response.ResultCode == ApiResponseResultCode.Ok)
                    {
                        return;
                    }
                    else if (response.ResultCode == ApiResponseResultCode.Exception)
                    {
                        if (response.ExceptionMessageForUser != null)
                        {
                            throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                        }
                        else
                        {
                            throw new Exception(response.ExceptionMessage ?? "");
                        }
                    }
                }
                else
                {
                    throw new Exception("No API response received.");
                }

                return;
            }
            catch (Exception exp)
            {

                throw;
            }

        }

        public async Task<bool> GetConflictAppointmentsOfOfficer(int officerId, int appointmentId, DateTime startDate, DateTime endDate)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "appointments" + "/GetConflictAppointmentsOfOfficer?officerId=" + officerId.ToString() + "&startDate=" + startDate.ToString("yyyy-MM-dd HH:mm:ss") + "&endDate=" + endDate.ToString("yyyy-MM-dd HH:mm:ss") + "&appointmentId=" + appointmentId.ToString();  // + "&tenantId=" + tenantId;

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<bool>? response = JsonConvert.DeserializeObject<ApiResponse<bool>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetAppointment({@appointmentId})", appointmentId);
                throw;
            }
        }

        public async Task<List<SmartpaMobiles.Data.Models.AppointmentService>> GetAllAppointmentServices(int officerId)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/" + "appointments/GetAllAppointmentServices?OfficerId=" + officerId.ToString();

                //Εκτελούμε το request.
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.Models.AppointmentService>> response = JsonConvert.DeserializeObject<ApiResponse<List<Data.Models.AppointmentService>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    throw new ApplicationException(response.ExceptionMessage ?? "");
                }

                return new List<Data.Models.AppointmentService>();
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex.Message, null);
                throw;
            }
        }
    }
}
