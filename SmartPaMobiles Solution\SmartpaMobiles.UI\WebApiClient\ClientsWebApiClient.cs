﻿using SmartpaMobiles.ApiShared;
using SmartpaMobiles.Shared;
using System.Text;
using Newtonsoft.Json;
using SmartpaMobiles.Data.Models;
using SmartpaMobiles.Data.DTOs;
using SmartpaMobiles.ApiShared.Request;
using SmartpaMobiles.ApiShared.Response;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using SmartpaMobiles.Shared.Authorization;


namespace SmartpaMobiles.UI.WebApiClient
{
    public class ClientsWebApiClient
    {
        private HttpClient httpClient;
        private string apiVersion = "v1";
        private ILogger<ClientsWebApiClient> logger;

        public ClientsWebApiClient(HttpClient httpClient, ILogger<ClientsWebApiClient> logger)
        {
            this.httpClient = httpClient;
            this.logger = logger;
        }

        public async Task<PagedData<List<SmartpaMobiles.Data.DTOs.ClientView>>> GetClients(int officerId, string filter, UInt16 pageIndex, UInt16 pageSize)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/" + "clients";

                RequestDataParameters requestDataParams = new RequestDataParameters();
                //requestDataParams.TenantId = tenantId;
                requestDataParams.OfficerId = officerId;
                requestDataParams.Filter = filter.Trim();
                requestDataParams.PageIndex = pageIndex;
                requestDataParams.PageSize = pageSize;
                requestDataParams.SortColumns = new Dictionary<string, SortOrder>();
                requestDataParams.SortColumns.Add("FirstLastName", SortOrder.Ascending);
                requestDataParams.SortColumns.Add("MiddleName", SortOrder.Ascending);
                string getEntitiesParamsJson = JsonConvert.SerializeObject(requestDataParams);  //Μετατρέπουμε τις παραμέτρους για το διάβασμα entities σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContent = new StringContent(getEntitiesParamsJson, Encoding.UTF8, "application/json");

                string urlParameters = UrlHelpers.ToQueryString(requestDataParams, "&");
                requestUri = requestUri + "?" + urlParameters;

                //Εκτελούμε το request.
                //this.httpClient.DefaultRequestHeaders.Authorization= new AuthenticationHeaderValue("Bearer", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOiIyY2FhYmI1NC1mZDM4LTRhY2MtOTMyYS1kNWM3Yjk0ZmRkMmYiLCJVc2VybmFtZSI6IjMzIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZSI6IlAgTSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IlN1cGVyQWRtaW4iLCJUZW5hbnRJZCI6IjNmYTg1ZjY0LTU3MTctNDU2Mi1iM2ZjLTJjOTYzZjY2YWZhNiIsImV4cCI6MTc0NzA0Mjk2Mn0.ZqUnCMKdLJ37E2PBZJJVTaFXpVBa5skxbHhBY18wTjs");
                //HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);  //Διαβάζουμε το HttpResponse   Δουλεύει ως GET           
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContent);  //Διαβάζουμε το HttpResponse

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<PagedData<List<Data.DTOs.ClientView>>> response = JsonConvert.DeserializeObject<ApiResponse<PagedData<List<Data.DTOs.ClientView>>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    throw new ApplicationException(response.ExceptionMessage ?? "");
                }

                return new PagedData<List<Data.DTOs.ClientView>> { Data = null, DataTotalCount = 0 };
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetClients({@officerId},{fiter},{@pageIndex}, {@pageSize})", officerId, filter, pageIndex, pageSize);
                throw;
            }
        }

        public async Task<List<SmartpaMobiles.Data.DTOs.ClientLI>> GetClientsLI(int officerId)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/" + "Clients/li?officerId=" + officerId.ToString();

                //Εκτελούμε το request.
                HttpResponseMessage httpResponse = await this.httpClient.GetAsync(requestUri);

                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<List<Data.DTOs.ClientLI>> response = JsonConvert.DeserializeObject<ApiResponse<List<Data.DTOs.ClientLI>>>(responseString)!;  //Μετατρέπουμε το δικό μας response σε object

                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent!;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    throw new ApplicationException(response.ExceptionMessage ?? "");
                }

                return new List<Data.DTOs.ClientLI>();
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetClientsLI({@offcerId})", officerId);
                throw;
            }
        }

        public async Task<Client?> GetClient(int clientId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "Clients" + "/" + clientId.ToString();  // + "&tenantId=" + tenantId;

                string responseString = await this.httpClient.GetStringAsync(requestUri);

                ApiResponse<Client?>? response = JsonConvert.DeserializeObject<ApiResponse<Data.Models.Client?>>(responseString);
                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in GetClient({@clientId})", clientId);
                throw;
            }
        }

        public async Task<Client?> CreateOrUpdateClient(Client client)
        {
            try
            {
                string requestUri = httpClient.BaseAddress + apiVersion + "/" + "clients/createorupdate";

                //string ClientJson = Newtonsoft.Json.JsonConvert.SerializeObject(Client, Newtonsoft.Json.Formatting.Indented);  //Μετατρέπουμε το Client object σε json.
                string ClientJson = System.Text.Json.JsonSerializer.Serialize(client);  //Μετατρέπουμε το Client object σε json.

                //Ετοιμάζουμε το HttpContext με τα json data.
                HttpContent httpContext = new StringContent(ClientJson, Encoding.UTF8, "application/json");

                //Εκτελούμε το POST request.
                HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse<Client>? response = JsonConvert.DeserializeObject<ApiResponse<Client>>(responseString);  //Μετατρέπουμε το δικό μας response σε object


                ///////////////
                //int[] arr = new int[100000];
                //Parallel.ForEach(arr, new ParallelOptions { MaxDegreeOfParallelism = 10},
                //async i =>
                //{
                //    // logic
                //    Client client2 = new Client();
                //    client2.TenantId = Guid.Parse("3FA85F64-5717-4562-B3FC-2C963F66AFA6");
                //    client2.FirstName = i.ToString();
                //    client2.LastName = Guid.NewGuid().ToString();
                //    client2.DateCreated= DateTime.Now;
                //    client2.DateModified= DateTime.Now;
                //    client2.ObjectState = ObjectState.Added;

                //    string ClientJson = System.Text.Json.JsonSerializer.Serialize(client2);  //Μετατρέπουμε το Client object σε json.

                //    //Ετοιμάζουμε το HttpContext με τα json data.
                //    HttpContent httpContext = new StringContent(ClientJson, Encoding.UTF8, "application/json");

                //    //Εκτελούμε το POST request.
                //    HttpResponseMessage httpResponse = await this.httpClient.PostAsync(requestUri, httpContext);  //Διαβάζουμε το HttpResponse
                //    string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                //    ApiResponse<Client>? response = JsonConvert.DeserializeObject<ApiResponse<Client>>(responseString);  //Μετατρέπουμε το δικό μας response σε object

                //});
                /////////////////

                if (response!.ResultCode == ApiResponseResultCode.Ok)
                {
                    return response.ResponseContent;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Error in CreateOrUpdateClient({@tenant})", client);
                throw;
            }
        }

        public async Task DeleteClient(int clientId)
        {
            try
            {
                string requestUri = this.httpClient.BaseAddress + apiVersion + "/" + "clients" + "?clientId=" + clientId.ToString();

                HttpResponseMessage httpResponse = await this.httpClient.DeleteAsync(requestUri);
                string responseString = await httpResponse.Content.ReadAsStringAsync();  //Διαβάζουμε το δικό μας response μέσα στο HttpResponse
                ApiResponse response = JsonConvert.DeserializeObject<ApiResponse>(responseString);
                if (response.ResultCode == ApiResponseResultCode.Ok)
                {
                    return;
                }
                else if (response.ResultCode == ApiResponseResultCode.Exception)
                {
                    if (response.ExceptionMessageForUser != null)
                    {
                        throw new ApplicationException(response.ExceptionMessageForUser ?? "");
                    }
                    else
                    {
                        throw new Exception(response.ExceptionMessage ?? "");
                    }
                }

                return;
            }
            catch (Exception exp)
            {

                throw;
            }

        }


    }
}
