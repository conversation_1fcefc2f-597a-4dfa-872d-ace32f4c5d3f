<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:Class="SmartpaMobiles.UI.Pages.ClientsPage"
             xmlns:project="clr-namespace:SmartpaMobiles.UI"
             xmlns:model="clr-namespace:SmartpaMobiles.Data.Models;assembly=SmartpaMobiles.Data"
             xmlns:DTOs="clr-namespace:SmartpaMobiles.Data.DTOs;assembly=SmartpaMobiles.Data"
             xmlns:local="clr-namespace:SmartpaMobiles.UI">

    <ContentPage.ToolbarItems>
        <ToolbarItem Text="{x:Static project:Resources.GlobalResources.Save}" Order="Primary" IconImageSource="add.png" Clicked="newContactBtn_Clicked">
        </ToolbarItem>
    </ContentPage.ToolbarItems>

    <Grid RowDefinitions="20,30,20,*" Padding="5">
        <Grid Grid.Row="1" ColumnDefinitions="*,250" >
            <!--<ImageButton Grid.Column="0" Source="add.png" Padding="9" HeightRequest="40" WidthRequest="40" Margin="2" HorizontalOptions="Start" Style="{StaticResource Primary}" Clicked="newContactBtn_Clicked"></ImageButton>-->
            <Border Grid.Column="1" Style="{StaticResource FlatBorder}"  >
                <SearchBar x:Name="searchBar" SearchButtonPressed="searchBar_SearchButtonPressed" HorizontalOptions="Fill" HeightRequest="20" TextChanged="searchBar_TextChanged"></SearchBar>
            </Border>
        </Grid>
        <RefreshView Grid.Row="3" x:Name="refreshView" IsRefreshing="{Binding isRefreshing}" Refreshing="RefreshCommand">
            <CollectionView x:Name="clientsCV" SelectionMode="Single" ItemsSource="{Binding Clients}" SelectionChanged="clientsCV_SelectionChanged" >
                <CollectionView.EmptyView>
                    <Label Text="{x:Static project:Resources.GlobalResources.NoData}" HorizontalTextAlignment="Center" />
                </CollectionView.EmptyView>
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="DTOs:ClientView">
                        <Grid Padding="0" Margin="2">
                            <Border Style="{StaticResource FlatBorder}" >
                                <HorizontalStackLayout>
                                    <toolkit:AvatarView Text="{Binding Initials}" Margin="0,0,5,0" WidthRequest="50"  BackgroundColor="{Binding Source={x:Static local:ColorGenerator.RandomColor}}" CornerRadius="25"></toolkit:AvatarView>
                                    <VerticalStackLayout WidthRequest="200">
                                        <Label Text="{Binding FirstLastName}" FontAttributes="Bold" LineBreakMode="NoWrap" />
                                        <Label Text="{Binding Mobile1}" />
                                        <Label Text="{Binding Email}" />
                                        <!--<BoxView HeightRequest="1" BackgroundColor="{x:StaticResource Gray100}"/>-->
                                    </VerticalStackLayout>
                                </HorizontalStackLayout>
                                <Border.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="ClientsTapGesture_Tapped"  CommandParameter="{Binding ClientId}">
                                    </TapGestureRecognizer>
                                </Border.GestureRecognizers>
                            </Border>
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>

        </RefreshView>
    </Grid>
</ContentPage>